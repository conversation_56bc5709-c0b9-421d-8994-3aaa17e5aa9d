import { useEffect, useState } from "react";
import { Box } from "@mui/material";
import Celebration from "./Celebration";
import OfferCard from "./OfferCard";

const VerticalScroller = () => {
  const [index, setIndex] = useState(0); 

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev === 0 ? 1 : 0));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Box
      sx={{
        height: "200px", 
        overflow: "hidden",
        position: "relative",
      }}
    >
      <Box
        sx={{
          height: "400px", 
          transition: "transform 0.6s ease-in-out",
          transform: `translateY(-${index * 200}px)`, 
        }}
      >
        <Box sx={{ height: "200px" }}>
          <Celebration />
        </Box>
        {/* <Box sx={{ height: "200px" }}>
          <OfferCard />
        </Box> */}
      </Box>
    </Box>
  );
};

export default VerticalScroller;
