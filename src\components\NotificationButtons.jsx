import React from 'react';
import { But<PERSON>, Box } from '@mui/material';
import { useTheme } from "@mui/material";

const NotificationButtons = ({ handleButtonClick, activeButton, isNarrowMobile, isWideScreen }) => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                width: 'auto',
                background: '#FFF',
                boxShadow: '0px 3.5113511085510254px 17.55675506591797px rgba(0, 0, 0, 0.05)',
                zIndex: 100,
            }}
        >
            <Button
                onClick={() => handleButtonClick('all')}
                style={{
                    color: '#1B3745',
                    width: isNarrowMobile ? '1.25rem' : isWideScreen ? '80px' : '160.23px',
                    fontSize: isNarrowMobile ? '7px' : isWideScreen ? '9px' : '11px',
                    backgroundColor: activeButton === 'all' ? '#62B6F3' : theme.palette.background.secondary,
                    borderRadius: 5,
                    color: activeButton === 'all' ? '#FDFBFF' : theme.palette.text.secondary,
                }}
            >
                All
            </Button>
            <Button
                onClick={() => handleButtonClick('notifications')}
                style={{
                    color: '#1B3745',
                    width: isNarrowMobile ? '1.25rem' : isWideScreen ? '80px' : '160.23px',
                    fontSize: isNarrowMobile ? '7px' : isWideScreen ? '9px' : '11px',
                    backgroundColor: activeButton === 'notifications' ? '#62B6F3' : theme.palette.background.secondary,
                    borderRadius: 5,
                    color: activeButton === 'notifications' ? '#FDFBFF' : theme.palette.text.secondary,
                }}
            >
                Notifications
            </Button>
            <Button
                onClick={() => handleButtonClick('events')}
                style={{
                    color: '#1B3745',
                    width: isNarrowMobile ? '1.25rem' : isWideScreen ? '80px' : '160.23px',
                    fontSize: isNarrowMobile ? '7px' : isWideScreen ? '9px' : '11px',
                    backgroundColor: activeButton === 'events' ? '#62B6F3' : theme.palette.background.secondary,
                    borderRadius: 5,
                    color: activeButton === 'events' ? '#FDFBFF' : theme.palette.text.secondary,
                }}
            >
                Events
            </Button>
            <Button
                onClick={() => handleButtonClick('meetings')}
                style={{
                    color: '#1B3745',
                    width: isNarrowMobile ? '1.25rem' : isWideScreen ? '80px' : '160.23px',
                    fontSize: isNarrowMobile ? '7px' : isWideScreen ? '9px' : '11px',
                    backgroundColor: activeButton === 'meetings' ? '#62B6F3' : theme.palette.background.secondary,
                    borderRadius: 5,
                    color: activeButton === 'meetings' ? '#FDFBFF' : theme.palette.text.secondary,
                }}
            >
                Meetings
            </Button>
        </Box>
    );
};

export default NotificationButtons;