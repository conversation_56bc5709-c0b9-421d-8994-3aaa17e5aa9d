import { Box, Typography } from '@mui/material';
import React from 'react';
// import MoreHorizIcon from '@mui/icons-material/MoreHoriz';

const LibraryCards = () => {
  const cardData = [
    { id: 1, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
    { id: 2, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
    { id: 3, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
    { id: 4, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
    { id: 5, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
    { id: 6, icon: "/icon1.png", title: "Adobe eSign Signature Authurity" },
  ];

  return (
    <Box
      sx={{
        display: 'flex',
        gap: 2,
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      {cardData.map((card) => (
        <Box
          key={card.id}
          sx={{
            width: '100%',
            maxWidth: "117px",
            height: '117px',
            boxShadow: '0 8px 15px rgba(0, 0, 0, 0.1)',
            display: 'flex',
            flexWrap: 'wrap',
            borderRadius: '10px',
            justifyContent: 'center',
            alignItems: 'center',
            px: '13px'
          }}
        >
          <Box
            sx={{
              mt: '12px',
              mb: '26px',
            }}
          >
            {/* <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 100 }}>
              <MoreHorizIcon sx={{ fontSize: 30, color: 'gray' }} />
            </Box> */}
            <Box
              sx={{
                width: '100%',
                maxWidth: "92px",
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Box
                component="img"
                src={card.icon}
                alt="Icon"
                sx={{
                  width: '27px',
                  objectFit: 'cover',
                  borderRadius: 2,
                  mb: '10px',
                  mt: '10px'
                }}
              />
            </Box>
            <Typography
              sx={{
                color: '#b0b0b0',
                textAlign: 'center',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 400,
                fontSize: '10px',
              }}
            >
              {card.title}
            </Typography>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default LibraryCards;
