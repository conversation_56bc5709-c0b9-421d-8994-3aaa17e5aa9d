import { Badge, Box, Typography, useTheme, Grid } from "@mui/material";
import MailOutline from "@mui/icons-material/MailOutline";
import { getTodaysEvents } from "@/Data/Events";
import { getFormattedDate, monthName } from "./HelperFunctions";
import CustomSlider from "./CustomSlider";
import useMediaQuery from "@mui/material/useMediaQuery";
import { breakpoints } from "@/helper/mediaQueries";
import { useQuery } from "@apollo/client";
import BasicModal from "./Modal";
import { useState } from "react";
import Image from "next/image";
import { useColor } from "@/components/ColorContext";

const Celebration = () => {
  const { color } = useColor();
  const prefersDarkMode = useMediaQuery("(prefers-color-scheme: dark)");

  const theme = useTheme();
  const [Data, setData] = useState(null); //to show details on home screen
  const [dataModal, setDataModal] = useState(false); //to show details on home screen
  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;
  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);

  // const getEvent = useQuery(getTodaysEvents);
  const date = getFormattedDate();
  // console.log(date);
  const { loading, error, data } = useQuery(getTodaysEvents, {
    // variables: { today: "2023-11-29" },
    variables: { today: date, category: "inperson" },
  });
  if (error) {
    console.log(error);
  }
  let eventData = [];
  data &&
    data.todaysEvents.forEach((element) => {
      eventData.push({
        title: element.title,
        description: element.details,
      });
    });
  // console.log(data);
  return (
    <>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: "10px",
          boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
          padding: "20px",
          minHeight:"210px",
          cursor: "default"
        }}
      >
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography
            variant="h6"
            component="h2"
            fontSize="1.3rem"
            fontWeight="700"
            sx={{ fontSize: "1.3rem" }}
          >
            Celebrations 
          </Typography>
          <Badge
            color="secondary"
            // size="small"
            // margin={111}
            badgeContent={eventData.length || 0}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            sx={{
              display: "block",
              ".MuiBadge-badge": {
                transform: "translate(-40%, 20%)",
                // height: "14px",
                // width: "2px", // Adjust these values as needed
              },
            }}
          >
            <Box>
              <Image
                src={
                  color == "white"
                    ? "/icons/calendar.png"
                    : "/icons/calendar2.png"
                }
                alt="calendar"
                width={30}
                height={50}
              />
            </Box>
          </Badge>
        </Box>
        <Box marginTop={1}>
          <Grid container spacing={2}>
            <Grid item xs={3} sm={3}>
              <Box
                sx={{ textAlign: "center", mx: {xs: '1px',md: '10px'}, marginTop: {xs:'10px',md:'10px'} }}
              >
                <Typography
                  variant="h4"
                  sx={{ color: theme.palette.text.purple, fontWeight: {xs:"400",md:"600"} }}
                >
                  {new Date().getDate()}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {monthName(new Date().getMonth())}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {new Date().getFullYear()}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={9} sm={9}>
              <BasicModal
                Data={Data}
                dataModal={dataModal}
                setDataModal={setDataModal}
              />
              <CustomSlider
                slides={eventData}
                autoplaySpeed={1500}
                setData={setData}
                setDataModal={setDataModal}
                design={{
                  background: theme.palette.background.primary,
                  borderRadius: "20px",
                  padding: "20px",
                  margin: "10px 0px ",
                  maxWidth: isSmallScreen
                    ? "100%"
                    : isMediumScreen
                    ? "600px"
                    : "800px",
                  // minWidth:
                  //   isSmallScreen || isLargeScreen || isXLargeScreen
                  //     ? "100%"
                  //     : isMediumScreen
                  //     ? "600px"
                  //     : "800px",
                  minHeight: "80px",
                  maxHeight: "110px",
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};
export default Celebration;


