// File: AnnouncementDialog.js
import React from "react";
import { Box, Typography, Dialog } from "@mui/material";
import { useTheme } from "@emotion/react";
// import ResponsiveBox from './ResponsiveBox';
import Image from "next/image";

const AnnouncementDialog = ({
  open,
  handleCloseModal,
  title,
  details,
  ResponsiveBox,
  imageUrl,
}) => {
  const theme = useTheme();
  return (
    <Dialog open={open} onClose={handleCloseModal} maxWidth="sm" fullWidth>
      <ResponsiveBox sx={{ height: "425px" }} disableBorder>
        {/* <Box borderTop="3px solid #b484cc" width="100%"></Box> */}
        <Box
          style={{
            backgroundColor: "#003e53",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            // marginTop: "10px",
            padding: "15px",
            borderTop: "3px solid #b484cc",
            borderBottom: "3px solid #b484cc",
          }}
        >
          <Image
            src="/images/HassanaLogos.png"
            alt="Hassana Logos"
            width={100}
            height={50}
          />
          <Typography style={{ color: "white" }}></Typography>{" "}
        </Box>
        <Box
          sx={{
            // position:"absolute",
            padding: {
              lg: "70px 29px 100px 25px",
              xl: "70px 29px 100px 25px",
              md: "70px 29px 100px 25px",
              sm: "70px 29px 90px 25px",
              xs: "70px 29px 70px 25px",
            },
          }}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "3px",
          }}
        >
          <Typography
            style={{
              // color: "#1B3745",
              marginBottom: "8px",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            {title}
          </Typography>
          <Box
            borderTop="3px solid #b484cc"
            width="40%"
            marginBottom="10px"
            // marginTop="100px"
          ></Box>
          <Typography
            style={{
              fontSize: "12px",

              marginBottom: "8px",
               color: "black",
              color: "#BBB",
            }}
          >
            {/* We are happy to announce that our colleague Othman AlGhamdi- Chief
            Finance Officer Has been blessed with a baby boy Abdulrahman */}
            {details}
          </Typography>
          {/* <Typography
            style={{
              fontSize: "12px",
              marginBottom: "8px",
              color: "#BBB",
              // color: "black",
            }}
          >
            Congratulations!!!
          </Typography>
          <Typography
            style={{
              fontSize: "12px",
              marginBottom: "8px",
              color: "#BBB",
              // color: "black",
            }}
          >
            We wish you joy and happiness as you celebrate this wonderful
            addition to your family
          </Typography> */}
        </Box>
        <Box>
          <Typography
            sx={{
              marginTop: "8px",
              paddingLeft: "27px",
              // color: "#1B3745",
              fontSize: "13px",
            }}
          >
            Human Resources Department
          </Typography>
        </Box>
      </ResponsiveBox>
    </Dialog>
  );
};

export default AnnouncementDialog;
