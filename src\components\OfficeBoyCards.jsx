import React from "react";
import { Typography, <PERSON>, Divide<PERSON>, But<PERSON> } from "@mui/material";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
// import '../styles/fonts.css';
// import useMediaQuery from "@mui/material/useMediaQuery";
import { useColor } from "@/components/ColorContext";
import { convertUtcToLocal, selectedColor } from "@/components/HelperFunctions";
import { MeetingRoom } from "@mui/icons-material";

//
const AnnouncementCard = ({
  startTime, endTime,
  title,
  heading,
  details,
  button,
  isCurrentAnnouncement,
}) => {
  const isTablet = useMediaQuery("(max-width:400px)");
  // console.log("convertUtcToLocal", startTime, (convertUtcToLocal(startTime)))
  return (
    <Box sx={{ padding: "20px" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="h5" fontWeight={700}  sx={{fontSize:{lg:"21px",md:"18px",sm:"15px",xs:"15px"},display:"flex"}}> {heading} </Typography>
        <Typography variant="body1" sx={{ background: "#EAD3FF", color: "#A665E1", padding:"5px 10px",fontSize:{lg:"12px",md:"10px",sm:"10px",xs:"10px"}, borderRadius: "5px", fontWeight:"700" }}>{`${convertUtcToLocal(startTime)} -   ${convertUtcToLocal(endTime)}`}</Typography>
      </Box>
      <Typography variant="body2" sx={{color: "#A665E1" }} fontWeight={700}> {button} </Typography>

      <Typography variant="h6" sx={{ marginTop: "10px",fontSize:{lg:"16px",md:"14px",sm:"14px",xs:"14px"}}} fontWeight={700}>{title&&<MeetingRoom/>}{title}</Typography>
      <Divider
        sx={{
          width: "50%",
          border: isCurrentAnnouncement
            ? "1px solid #A665E1"
            : "1px solid #00BC82",
        }}
      />
      {/* <Typography variant="h6">{content}</Typography> */}
      <Typography variant="body2" sx={{ marginTop: "15px", overflow: "hidden", textOverflow: "ellipsis", display: "-webkit-box", WebkitBoxOrient: "vertical", WebkitLineClamp: 2 }}>
  {details}
</Typography>

    </Box>
  );
};

export default AnnouncementCard;






// export function convertUtcToLocal(utcDateTimeString) {
//   const utcDate = new Date(utcDateTimeString);

//   const localTime = `${utcDate.getHours()}:${utcDate.getMinutes()}`

//   return localTime;
// }