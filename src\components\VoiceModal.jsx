import { useSession } from 'next-auth/react';
import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Stack,
} from '@mui/material';
import axios from 'axios';
import { baseUrl } from '@/Data/ApolloClient';
import { lightTheme } from '@/theme';
import ThankYouModal from './NewModal';

const voice = '/v1/user-feedback';

export default function VoiceMattersModal({ open, handleClose }) {
  const { data: session } = useSession();
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('complaint');
  const [activeType, setActiveType] = useState(true);
  const [successOpen, setSuccessOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!subject.trim() || !description.trim()) {
      alert('Please fill in both Subject and Description.');
      return;
    }

    if (!session?.user?.id || !session?.accessToken) {
      alert('User session is invalid. Please log in again.');
      return;
    }

    const payload = {
      type,
      subject: subject.trim(),
      description: description.trim(),
    };

    try {
      setSubmitting(true);
      const response = await axios.post(`${baseUrl}${voice}`, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.accessToken}`,
        },
      });

      console.log('✅ Feedback submitted:', response.data);

      // Reset form
      setSubject('');
      setDescription('');
      setType('complaint');
      setActiveType(true);
      setSuccessOpen(true);

      // Close after showing success modal
      setTimeout(() => {
        setSuccessOpen(false);
        handleClose();
        setSubmitting(false);
      }, 1200);
    } catch (error) {
      console.error('❌ Submission failed:', error.response?.data || error.message);
      alert('Submission failed. Please try again.');
      setSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{ sx: { borderRadius: 3 } }}
    >
      <Box
        sx={{
          backgroundColor: lightTheme.palette.background.header,
          borderTop: `4px solid ${lightTheme.palette.text.purple}`,
          borderBottom: `4px solid ${lightTheme.palette.text.purple}`,
          color: '#fff',
          px: { xs: 2, sm: 3, md: 5 },
          py: 1.5,
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: 'center',
          textAlign: { xs: 'center', sm: 'left' },
        }}
      >
        <img src="../images/logowhite.png" alt="Logo" style={{ height: 30 }} />
        <Stack direction="column" spacing={1} alignItems="end">
          <img src="../images/arabic.png" alt="Arabic Text" style={{ height: 20, width: 200 }} />
          <img src="../images/voice.png" alt="Voice Matters" style={{ height: 12, width: 120 }} />
        </Stack>
      </Box>

      <DialogTitle sx={{ fontSize: 18, fontWeight: 'bold', pt: 2, pb: 1 }}>
        Your Voice Matters
      </DialogTitle>

      <DialogContent>
        <Box sx={{ display: 'flex', mb: 2, boxShadow: 1, borderRadius: 2, p: 1 }}>
          <Button
            onClick={() => {
              setType('complaint');
              setActiveType(true);
            }}
            variant="text"
            sx={{
              flex: 1,
              borderRadius: 2,
              backgroundColor: activeType ? '#8bc1ff !important' : 'transparent',
              color: activeType ? '#000' : '#666',
              borderColor: '#a3d2ff',
              fontWeight: 'medium',
            }}
          >
            Complaints
          </Button>
          <Button
            onClick={() => {
              setType('suggestion');
              setActiveType(false);
            }}
            variant="text"
            sx={{
              flex: 1,
              borderRadius: 2,
              backgroundColor: !activeType ? '#8bc1ff !important' : 'transparent',
              color: !activeType ? '#000' : '#666',
              borderColor: '#a3d2ff',
              fontWeight: 'medium',
            }}
          >
            Suggestion
          </Button>
        </Box>

        <TextField
          fullWidth
          label="Subject"
          variant="outlined"
          size="small"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          sx={{ mb: 2 }}
        />
        <TextField
          fullWidth
          multiline
          rows={4}
          label="Description"
          variant="outlined"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </DialogContent>

      <DialogActions sx={{ px: 2, pb: 2, pt: 1 }}>
        <Box sx={{ display: 'flex', width: '98%', gap: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleClose}
            disabled={submitting}
            sx={{
              borderColor: '#a66bcb',
              color: '#a66bcb',
              fontWeight: 'bold',
              borderRadius: 2,
              py: 1.5,
              '&:hover': {
                borderColor: '#914db9',
                backgroundColor: '#f9f2fd',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleSubmit}
            disabled={submitting}
            sx={{
              borderColor: '#a66bcb',
              color: '#a66bcb',
              fontWeight: 'bold',
              borderRadius: 2,
              py: 1.5,
              '&:hover': {
                backgroundColor: '#914db9',
                color: '#fff',
              },
            }}
          >
            {submitting ? 'Submitting...' : 'Confirm'}
          </Button>
        </Box>
      </DialogActions>

      <ThankYouModal open={successOpen} onClose={() => setSuccessOpen(false)} />
    </Dialog>
  );
}
