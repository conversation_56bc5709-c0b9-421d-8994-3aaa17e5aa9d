import { createTheme } from '@mui/material/styles';

export const lightTheme = createTheme({
    palette: {
        type: 'light',
        background: {
            primary: '#FCFAFF',
            secondary: '#fff',
            launchpad: '#F0EFFB',
            header:"#063F53",
            tracker:"#fff",
            
        },
        text: {
            primary: '#000',
            secondary: '#A7A7A7',
            green: '#00BC82',
            blue: '#62B6F3',
            purple: '#A665E1',
            white: '#fff',
           gray:'#8D8D8D',
           back: '#fff',
        },
        headcolor:{
            main: '#1B3745'
        },

        blue: {
            main: '#62B6F3'
        },
        purple: {
            main: '#A665E1'
        },
        green: {
            main: '#00BC82'
        },
        white: {
            main: '#fff'
        },

    },
});
export const darkTheme = createTheme({
    palette: {
        type: 'dark',
        background: {
            primary: '#252525',
            secondary: '#383838',
            launchpad: '#383838',
            header:"#252525",
            tracker:"#383838",
        },
        text: {
            primary: '#fff',
            secondary: '#A7A7A7',
            green: '#00BC82',
            blue: '#62B6F3',
            purple: '#A665E1',
            white: '#fff',
            back: '#383838',
        },
        headcolor: {
            main: '#fff'
        },
        blue: {
            main: '#62B6F3'
        },
        purple: {
            main: '#A665E1'
        },
        green: {
            main: '#00BC82'
        },
        white: {
            main: '#fff'
        },

    }
});