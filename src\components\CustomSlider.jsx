import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Box, Typography } from "@mui/material";

const CustomSlider = (props) => {
  const { slides, design, setData, setDataModal } = props;

  const settings = {
    infinite: true,
    speed: 2000,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    // autoplaySpeed: props.autoplaySpeed || 2000,
    autoplaySpeed: 5000,
    arrows: false,
  };
  return (
    <Slider {...settings}>
      {slides.length > 0 ? (
        slides.map((slide, index) => {
          console.log(slide.title, "==============")
          return (
            <Box
              sx={{ ...design, padding:"10px 20px" }}
              key={index}
              onClick={() => {
                setData({ title: slide.title, details: slide.description });
                setDataModal(true);
                console.log("dataset");
              }}
            >
              <Typography variant="body2" sx={{ fontSize: "1rem" }}>
                {slide.title.length > 31
                  ? slide.title.substring(0, 31) + "..."
                  : slide.title}
              </Typography>

              <Box
                sx={{
                  height: "3px",
                  width: "40%",
                  background: "#00BC82",
                  marginY: "5px",
                }}
              />
              <Typography
                variant="body2"
                sx={{
                  fontSize: "10px",
                  lineHeight: "1.5",
                  color: "#A7A7A7",
                  fontSize: "12px",
                }}
              >
                {slide.description.length > 80
                  ? slide.description.substring(0, 80) + "..."
                  : slide.description}
              </Typography>
            </Box>
          );
        })
      ) : (
        <Box sx={{ ...design, width: "30vw" }}>
          <Typography
            variant="body2"
            sx={{ fontWeight: 700, textAlign: "center",fontSize:"16px" }}
          >
            No event for today
          </Typography>
        </Box>
      )}
    </Slider>
  );
};

export default CustomSlider;
