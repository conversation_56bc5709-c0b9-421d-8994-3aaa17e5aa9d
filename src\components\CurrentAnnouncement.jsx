// AnnouncementItem.js
import AnnouncementCard from "@/components/AnnouncementCard";
import React from "react";
import { useTheme } from "@emotion/react";
import { borderRadius, Box } from "@mui/system";
// import ResponsiveBox from './ResponsiveBox';
// import AnnouncementCard from './AnnouncementCard';

const AnnouncementItem = ({
  announcement,
  handleOpenModal,
  ResponsiveBox,
  isCurrentAnnouncement,
  setSelectedAnnouncement,
}) => {
  const theme = useTheme();
  return (
    <ResponsiveBox
      sx={{
        padding: "30.41px 16.91px 29.04px 16.91px",
        marginTop: "20px",
        borderRadius:"10px"
      }}
      isCurrentAnnouncement={isCurrentAnnouncement}
    >
    {/* <ResponsiveBox
    ResponsiveBox
    sx={{
      background: theme.palette.background.secondary,
      padding: "30.41px 16.91px 29.04px 16.91px",
      // padding: "10px",
      marginTop: "10px",
    }}
    isCurrentAnnouncement={isCurrentAnnouncement}
    > */}
      <AnnouncementCard
        title={announcement.title}
        details={announcement.details}
        onClick={() => {
          handleOpenModal(announcement);
          setSelectedAnnouncement(announcement);
        }}
        isCurrentAnnouncement={isCurrentAnnouncement}
        />
    {/* </ResponsiveBox> */}
    </ResponsiveBox>
  );
};

export default AnnouncementItem;
