import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useColor } from "@/components/ColorContext";
import {
  formatDateTimeUTC,
  selectedColor,
  separateLatestData,
} from "@/components/HelperFunctions";
import { getExternalNews, getInternalNews, getAllNews } from "@/Data/News";
import { Typography } from "@mui/material";
import Link from "next/link";

const NewsList = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMediumScreen1 = useMediaQuery("(max-width:967px)");
  const theme = useTheme();
  const { color } = useColor();

  // Fetch news data when component mounts
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        const newsData = await getAllNews(); // Call getAllNews to fetch data
        setNews(newsData);
        setLoading(false);
      } catch (err) {
        setError("Failed to load news. Please try again later.");
        setLoading(false);
      }
    };
    fetchNews();
  }, []); // Empty dependency array means this runs once on mount


  
  return (
    <Box sx={{ maxHeight: "650px", overflow: "auto" }}>
      <Grid container spacing={1} marginLeft={1} sx={{ marginBottom: "60px" }}>
        {loading ? (
          <Box sx={{ margin: "auto" }}>
            <Typography variant="h6">Loading news...</Typography>
          </Box>
        ) : error ? (
          <Box sx={{ margin: "auto" }}>
            <Typography variant="h6">{error}</Typography>
          </Box>
        ) : news && news.length > 0 ? (
          <>
            {news.map((newsItem, index) => (
              <Grid item xs={12} sm={12} md={6} lg={4} key={index}>
                <Box
                  style={{
                    margin: isMediumScreen1 ? "30px 8px" : "10px 16px",
                    width: "80%",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "15px",
                      background: "#FCFAFF",
                      background: theme.palette.background.primary,
                    }}
                  >
                  <img
                    src={newsItem.featuredImage.replace(
                      "http://localhost:3009",
                      "https://hassana-api.360xpertsolutions.com"
                    )}
                    alt="News Image"
                    style={{ width: "100%",height: "200px", objectFit: "contain" }}
                    loading="lazy"
                  />
                    <p
                      style={{
                        color: "#00BC82",
                        fontSize: "14px",
                        fontWeight: 400,
                        fontFamily: "Helvetica",
                      }}
                    >
                      {formatDateTimeUTC(newsItem.publication)}
                    </p>
                    <p
                      style={{
                        fontFamily: "Urbanist",
                        fontWeight: 700,
                        fontSize: "18px",
                        wordWrap: "break-word",
                      }}
                    >
                      {newsItem.title.substring(0, 100)}...
                    </p>
                    <Link href={newsItem.url}>
                      <Button
                        variant="contained"
                        href={newsItem.url}
                        sx={{
                          borderRadius: 1,
                          width: "40%",
                          fontSize: "10px",
                          background: "#62B6F3",
                        }}
                      >
                        Learn more
                      </Button>
                    </Link>
                  </div>
                </Box>
              </Grid>
            ))}
          </>
        ) : (
          <Box sx={{ margin: "auto" }}>
            <Typography variant="h6">No news found</Typography>
          </Box>
        )}
      </Grid>
    </Box>
  );
};

export default NewsList;