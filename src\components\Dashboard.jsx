import * as React from "react";
import { styled, ThemeProvider } from "@mui/material/styles";
import { useTheme } from "@mui/material";
import CssBaseline from "@mui/material/CssBaseline";
import Box from "@mui/material/Box";
import useMediaQuery from "@mui/material/useMediaQuery";
import Toolbar from "@mui/material/Toolbar";
import Layout from "./Layout";
import Header from "./Header/Header";
import { darkTheme, lightTheme } from "@/theme";
import { ColorProvider } from "./ColorContext";
import { DrawerProvider } from "./Header/DrawerProvider";
import { useMode } from "./ModeContext";
import View from "./View";

export default function MyDashboard(props) {
  const [open, setOpen] = React.useState(false);

  const theme = useTheme();
  const { mode } = useMode();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.between("sm", "md"));

  let mainDivWidth;
  if (isMobile) {
    mainDivWidth = "100%";
  } else if (isMediumScreen) {
    mainDivWidth = "75rem";
  } else {
    mainDivWidth = "90rem";
  }
  const { children } = props;

  return (
    <ThemeProvider theme={mode == "light" ? lightTheme : darkTheme}>
      <ColorProvider>
        {mode && (
          <Box sx={{ display: "flex" }}>
            <CssBaseline />
            <Header />
            <View>{children}</View>
          </Box>
        )}
      </ColorProvider>
    </ThemeProvider>
  );
}
