import React, { createContext, useContext, useState, useEffect } from "react";

const ModeContext = createContext({
  mode: "light",
  setMode: () => {},
});

export const ModeProvider = ({ children }) => {
  const [mode, setMode] = useState(() => {
    console.log("Initializing mode state");
    if (typeof window !== "undefined") {
      console.log("Running on client-side, checking localStorage for mode");
      return localStorage.getItem("mode") || "light";
    }
    return "light";
  });

  useEffect(() => {
    console.log("Mode state changed, syncing to localStorage");
    if (typeof window !== "undefined") {
      localStorage.setItem("mode", mode);
    }
  }, [mode]);

  console.log("Rendering ModeProvider with mode:", mode);

  return (
    <ModeContext.Provider value={{ mode, setMode }}>
      {children}
    </ModeContext.Provider>
  );
};

export const useMode = () => {
  const context = useContext(ModeContext);
  if (!context) {
    throw new Error("useMode must be used within a ModeProvider");
  }
  return context;
};
