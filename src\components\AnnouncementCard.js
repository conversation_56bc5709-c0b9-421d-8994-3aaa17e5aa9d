import React, { useState } from "react";
import { Typo<PERSON>, Box, Divider, Modal, IconButton } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import { useMediaQuery, useTheme } from "@mui/material";

const AnnouncementCard = ({
  title,
  details,
  isSelected,
  onClick,
  borderLeft,
  isCurrentAnnouncement,
  showDate,
  date,
  image,
}) => {
  const [openImageModal, setOpenImageModal] = useState(false);
  const isMediumScreen = useMediaQuery("(max-width: 1200px)");
  const theme = useTheme();
  const cleanImagePath = image ? image.replace('http://localhost:3009', '') : null;
  const imgUpdate = 'https://hassana-api.360xpertsolutions.com' + cleanImagePath;

  const handleCardClick = (e) => {
    if (image && !e.target.closest('a, button')) {
      setOpenImageModal(true);
    }
    if (onClick) onClick(e);
  };

  const handleCloseModal = (e) => {
    e.stopPropagation();
    setOpenImageModal(false);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: isMediumScreen ? "center" : "flex-start",
        gap: "15px",
        height: "100%",
        backgroundColor: 'blue',
        cursor: image ? "pointer" : "default",
      }}
      onClick={handleCardClick}
    >
      {/* Image container - only render if image exists */}
      {/* {image && (
        <Box
          sx={{
            width: "100%",
            height: "200px",
            overflow: "hidden",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#f5f5f5",
            cursor: "zoom-in",
          }}
        >
          <img
            src={imgUpdate}
            alt={title}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
        </Box>
      )} */}
      
      {/* Content section */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: isMediumScreen ? "center" : "flex-start",
          gap: "7.5px",
          width: "100%",
          padding: "0 16px",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            fontSize: "16px",
            fontFamily: "Urbanist",
            wordWrap: "break-word",
            width: "100%",
          }}
        >
          {title}
        </Typography>
        <Divider
          sx={{
            width: "122px",
            border: isCurrentAnnouncement
              ? "1px solid #A665E1"
              : "1px solid #00BC82",
          }}
        />
      </Box>
      
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.text.primary,
          fontWeight: 500,
          fontFamily: "Inter",
          wordWrap: "normal",
          fontSize: "14px",
          wordWrap: "break-word",
          padding: "0 16px 16px",
          width: "100%",
        }}
      >
        {details}
      </Typography>
      
      {/* Date display if showDate is true */}
      {showDate && date && (
        <Typography
          variant="caption"
          sx={{
            color: theme.palette.text.secondary,
            fontFamily: "Inter",
            padding: "0 16px 16px",
            width: "100%",
          }}
        >
          {new Date(date).toLocaleDateString()}
        </Typography>
      )}

      {/* Image Modal */}
      {image&&<Modal
        open={openImageModal}
        onClose={handleCloseModal}
        aria-labelledby="image-modal"
        aria-describedby="image-modal-description"
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backdropFilter: "blur(5px)",
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: isMediumScreen ? '90%' : '70%',
            height: isMediumScreen ? 'auto' : '80%',
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 2,
            display: 'flex',
            flexDirection: isMediumScreen ? 'column' : 'row',
            borderRadius: '8px',
            overflow: 'hidden',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <IconButton
            onClick={handleCloseModal}
            sx={{
              position: 'absolute',
              right: 10,
              top: 10,
              zIndex: 1,
              backgroundColor: 'rgba(0,0,0,0.5)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(0,0,0,0.7)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
          
          {/* Image section (right side) */}
          <Box
            sx={{
              width: isMediumScreen ? '100%' : '50%',
              height: isMediumScreen ? '300px' : '100%',
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#f5f5f5',
            }}
          >
            <img
              src={imgUpdate}
              alt={title}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
              }}
            />
          </Box>
          
          {/* Details section (left side) */}
          <Box
            sx={{
              width: isMediumScreen ? '100%' : '50%',
              padding: '20px',
              overflowY: 'auto',
            }}
          >
            <Typography variant="h5" gutterBottom>
              {title}
            </Typography>
            
            {showDate && date && (
              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                {new Date(date).toLocaleDateString()}
              </Typography>
            )}
            
            <Divider sx={{ my: 0 }} />
            
            <Typography variant="body1">
              {details}
            </Typography>
          </Box>
        </Box>
      </Modal>}
    </Box>
  );
};

export default AnnouncementCard;