import React, { useState ,useEffect} from "react";
import Button from "@mui/material/Button";
import ButtonGroup from "@mui/material/ButtonGroup";
import Grid from "@mui/material/Grid";
import Hidden from "@mui/material/Hidden";
import Image from 'next/image';
import Link from "next/link";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import MenuIcon from "@mui/icons-material/Menu";
import LoginSlideForm from "@/components/login";
import { useRouter } from "next/router";

function AppBar() {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const [anchorEl, setAnchorEl] = useState(null);

  const handleClose = () => {
    setIsOpen(false);
  };

  const toggleForm = () => {
    setIsOpen(!isOpen);
  };

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  useEffect(() => {
    const login = router.query.login;
    if (login == 'false') {
      setIsOpen(true);
    }
  }, [router.query]);

  return (
    <Grid
      container
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{
        top: "0px",
        left: "0px",
        width: "100%",
        position: "fixed",
        backgroundColor: "#063F53",
        borderTop: "4px solid #A665E1",
        borderBottom: "4px solid #A665E1",
        padding: "10px",
        zIndex: 99999,
      }}
    >
      <Grid item sx={{ marginLeft: "40px" }}>
        <Image
          width={200}
          height={42}
          // sx={{ marginLeft: "20rem" }}
          src="/HassanaLogoD.png"
          loading="lazy"
          alt="Logo"
        />
      </Grid>
      <Grid item xs style={{ flexGrow: 1 }}></Grid>
      <Hidden only={["sm", "md", "lg", "xl"]}>
        <Button
          aria-controls="simple-menu"
          aria-haspopup="true"
          onClick={handleClick}
        >
          <MenuIcon sx={{ color: "white" }} />
        </Button>
        <Menu
          id="simple-menu"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleCloseMenu}
        >
          <MenuItem onClick={handleCloseMenu}>About Us</MenuItem>
          <MenuItem onClick={toggleForm}>Login</MenuItem>
        </Menu>
      </Hidden>
      <Hidden only={["xs"]}>
        <ButtonGroup
          sx={{ marginRight: "30px" }}
          disableElevation
          variant="contained"
          aria-label="Disabled elevation buttons"
        >
          <Button
            sx={{
              marginRight: "30px",
              backgroundColor: "#063F53",
              "&:hover": {
                backgroundColor: "grey",
              },
            }}
          />
          <Link href="https://hassana.com.sa/" target="_blank">
          <Button
            sx={{
              marginRight: "30px",
              paddingRight: "50px",
              backgroundColor: "#063F53",
              "&:hover": {
                backgroundColor: "grey",
              },
            }}
          >
            About Us
          </Button>
          </Link>
          <Button
            onClick={toggleForm}
            sx={{
              backgroundColor: "#063F53",
              "&:hover": {
                backgroundColor: "grey",
              },
            }}
          >
            Login
          </Button>
        </ButtonGroup>
      </Hidden>
      {isOpen && <LoginSlideForm isOpen={isOpen} handleClose={handleClose} />}
    </Grid>
  );
}

export default AppBar;
