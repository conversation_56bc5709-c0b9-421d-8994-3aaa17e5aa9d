import React, { useState } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import Image from 'next/image';
import Slide from "@mui/material/Slide";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import { Box } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { createTheme, ThemeProvider } from "@mui/material/styles";

const theme = createTheme({
  palette: {
    primary: {
      main: "#00bc82",
    },
    secondary: {
      main: "#a665e1",
    },
    text: {
      primary: "#ffffff",
    },
  },
  typography: {
    fontFamily: "Urbanist-Bold, Helvetica",
  },
});

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SlideInPopup = () => {
  const [open, setOpen] = useState(false);
  const isSmallScreen = useMediaQuery("(max-width:600px)");
  const isMediumScreen = useMediaQuery("(max-width:960px)");
  const isMediumScreen2 = useMediaQuery("(max-width:894px)");
  const isMediumScreen3 = useMediaQuery("(max-width:774px)");
  const isMediumScreen4 = useMediaQuery("(max-width:705px)");
  const isMediumScreen5 = useMediaQuery("(max-width:600px)");

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <ThemeProvider theme={theme}>
      <div>
        <Button variant="outlined" onClick={handleClickOpen}>
          Open Sliding Popup
        </Button>
        <Dialog
          open={open}
          onClose={handleClose}
          TransitionComponent={Transition}
          keepMounted
          fullWidth
          maxWidth="sm"
          PaperProps={{
            style: {
              width: "962px",
              height: "592px",
              backgroundImage: "url('/Images/Rectanglebackground.png')",
              backgroundSize: "cover",
              maxWidth: "none",
            },
          }}
        >
          <DialogContent
            style={{
              textAlign: "center",
              backdropFilter: "blur(1px)",
              background:
                "linear-gradient(180deg, rgba(0, 0, 0, 0.33) 0%, rgba(255, 255, 255, 0.33))",
              display: "flex",
              flexDirection: "column",

              alignItems: "center",
            }}
            sx={{
              overflowY: "auto",
              scrollbarWidth: "thin", // For Firefox
              scrollbarColor: "transparent transparent", // For Firefox

              /* For WebKit browsers (Chrome, Safari) */
              "&::-webkit-scrollbar": {
                width: 8,
              },

              "&::-webkit-scrollbar-thumb": {
                backgroundColor: "transparent",
              },
            }}
          >
            <img
              src="/Images/HassanaLogos.png"
              alt="Your Image Alt Text"
              style={{ marginBottom: "16.16px" }}
            />
            <div
              className="Line8"
              style={{
                width: "100%",
                height: "0",
                border: "1px #00BC82 solid",
                margin: "16.16px auto",
                maxWidth: "572px",
              }}
            ></div>{" "}
            <Typography
              variant="h6"
              style={{
                width: "91%",
                fontFamily: "Urbanist-SemiBold, Helvetica",
                fontWeight: "bold",
                fontSize: "16px",
                textAlign: "center",
                color: "white",
                // margin: "32px auto 0",
              }}
            >
              Our recommendation for you to enjoy the weekend
            </Typography>
            <Grid container sx={{ marginTop: { sm: "32px" } }}>
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  marginTop: { xs: "32px", sm: "32px", md: "0" },
                  order: { xs: 2, sm: 2, md: 1 },
                }}
              >
                <Box
                  sx={{
                    maxWidth: {
                      lg: "443px",
                      md: "90%",
                      "@media (max-width: 702px)": {
                        width: "308px",
                      },
                    },

                    height: {
                      xs: "220px",
                      sm: "220px",
                      md: "219px",
                      lg: "247px",
                    },
                    position: "absolute",
                    top: "145px",
                    left: isSmallScreen || isMediumScreen ? "50%" : "30px",
                    transform:
                      isSmallScreen || isMediumScreen
                        ? "translateX(-50%)"
                        : "translateX(0)",
                    marginTop: "32px",
                    borderRadius: "10px",
                    backdropFilter: "blur(1px) brightness(100%)",
                    WebkitBackdropFilter: "blur(1px) brightness(100%)",
                    background:
                      "linear-gradient(180deg, rgba(255, 255, 255, 0.33) 0%, rgba(255, 255, 255, 0.33) 100%)",
                    padding: "5px",
                    boxSizing: "border-box",
                    display: "flex",
                  }}
                >
                  <div
                    style={{
                      // flex: 1,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "flex-start",
                      paddingTop: 5,
                    }}
                  >
                    <Typography
                      style={{
                        color: "white",
                        fontSize: 16,
                        fontFamily: "Urbanist",
                        fontWeight: "600",
                        wordWrap: "break-word",
                        marginBottom: "20px",
                        alignSelf: "flex-start",
                        marginLeft: "15px",
                      }}
                    >
                      Where to go !!!
                    </Typography>
                    <img
                      style={{
                        width: isMediumScreen3 ? "104px" : "117px", // Adjust the width here
                        height: isMediumScreen3 ? "90px" : "113px",
                        marginLeft: "15px",
                      }}
                      src="/Images/Rectangle.png"
                      alt="Rectangle"
                    />
                  </div>
                  <div
                    style={{
                      // flex: 4,
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                      marginLeft: "20px",
                      marginTop: "48px",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        marginBottom: "8px",
                      }}
                    >
                      <Typography
                        sx={{
                          color: "white",
                          fontSize: isMediumScreen3 ? "14px" : "16px",
                          fontFamily: "Urbanist",
                          fontWeight: "700",
                          wordWrap: "break-word",
                          textAlign: "left",
                          marginRight: "10px",
                        }}
                      >
                        Hidden Table
                      </Typography>
                      <Button
                        variant="contained"
                        style={{
                          fontFamily: "Urbanist-Bold, Helvetica",
                          fontWeight: "bold",
                          fontSize: isMediumScreen2
                            ? "8px"
                            : isMediumScreen3
                            ? "6px"
                            : "12px",
                          color: "#00bc82",
                          backgroundColor: "white",
                          borderRadius: 30,
                          whiteSpace: "nowrap",
                          marginLeft: isMediumScreen4 ? "-4px" : "10px", // Adjust the spacing between buttons
                        }}
                      >
                        Activity
                      </Button>
                    </div>

                    <Typography
                      variant="subtitle2"
                      style={{
                        color: "white",
                        fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                        fontFamily: "Urbanist",
                        fontWeight: "600",
                        wordWrap: "break-word",
                        textAlign: "left",
                      }}
                    >
                      Arrival time:{" "}
                      <span
                        style={{
                          color: "white",
                          fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                          fontFamily: "Urbanist",
                          fontWeight: "600",
                          wordWrap: "break-word",
                        }}
                      >
                        8:00pm
                      </span>
                    </Typography>

                    <Typography
                      variant="subtitle2"
                      style={{
                        color: "white",
                        fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                        fontFamily: "Urbanist",
                        fontWeight: "600",
                        wordWrap: "break-word",
                        textAlign: "left",
                        marginBottom: "8px",
                      }}
                    >
                      Dinner Time:{" "}
                      <span
                        style={{
                          color: "white",
                          fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                          fontFamily: "Urbanist",
                          fontWeight: "600",
                          wordWrap: "break-word",
                        }}
                      >
                        9:00pm - 12:00pm
                      </span>
                    </Typography>

                    <Typography
                      variant="subtitle2"
                      style={{
                        color: "white",
                        fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                        fontFamily: "Urbanist",
                        fontWeight: "600",
                        wordWrap: "break-word",
                        textAlign: "left",
                      }}
                    >
                      Booking:{" "}
                      <span
                        style={{
                          color: "white",
                          fontSize: isMediumScreen3 ? "11px" : "14px", // Adjust the font size here
                          fontFamily: "Urbanist",
                          fontWeight: "600",
                          wordWrap: "break-word",
                        }}
                      >
                        www.yourwebsite.com
                      </span>
                    </Typography>
                    <Button
                      variant="contained"
                      style={{
                        fontFamily: "Urbanist-Bold, Helvetica",
                        fontWeight: "bold",
                        fontSize: isMediumScreen2
                          ? "8px"
                          : isMediumScreen3
                          ? "6px"
                          : "12px",
                        color: "#ffffff",
                        backgroundColor: "#a665e1",
                        borderRadius: 30,
                        whiteSpace: "nowrap",
                        marginTop: "7px",
                      }}
                    >
                      Book now
                    </Button>
                  </div>
                </Box>
                <Typography
                  style={{
                    fontWeight: "bold",
                    fontSize: "32px",
                    color: "white",
                    marginTop: isMediumScreen
                      ? "210px"
                      : isSmallScreen
                      ? "60px"
                      : "300px",
                    marginLeft: isMediumScreen
                      ? "0px"
                      : isSmallScreen
                      ? "60px"
                      : "300",
                  }}
                >
                  Happy Weekend!!!
                </Typography>
              </Grid>
              <Grid
                item
                xs={12}
                sm={6}
                width="443"
                style={{
                  marginTop: isMediumScreen ? "32px" : "0",
                  order: isMediumScreen ? 1 : 2,
                }}
              >
                <Typography
                  variant="subtitle1"
                  style={{
                    fontWeight: "bold",
                    textAlign: "left",
                    marginTop: isMediumScreen
                      ? "300px"
                      : isSmallScreen
                      ? "60px"
                      : "75px",
                    color: "white",
                    position: "absolute",
                    marginLeft: "10px",
                    left: "53%",
                    transform:
                      isSmallScreen || isMediumScreen
                        ? "translateX(-50%)"
                        : "translateX(0)",
                    wordWrap: "break-word",
                  }}
                  sx={{
                    fontSize: {
                      xs: "20px",
                      sm: "23.6px",
                    },
                  }}
                >
                  Never do tomorrow
                  <br />
                  what you can do today.
                  <br />
                  Procrastination is the thief of time.
                </Typography>
                <Typography
                  variant="subtitle1"
                  style={{
                    fontWeight: "medium",
                    fontSize: 18,
                    color: "white",
                    marginTop: isMediumScreen
                      ? "500px"
                      : isSmallScreen
                      ? "150px"
                      : isMediumScreen5
                      ? "450px"
                      : "200px",
                    position: "absolute",
                    marginLeft: isSmallScreen || isMediumScreen ? "0" : "35px",
                    left: "50%",
                    textAlign: "left",
                    transform:
                      isSmallScreen || isMediumScreen
                        ? "translateX(-50%)"
                        : "translateX(0)",
                  }}
                >
                  Charles Dickens
                </Typography>
                <Box
                  style={{
                    display: "flex",
                    gap: "113px",
                    position: "absolute",
                    marginTop: isMediumScreen
                      ? "550px"
                      : isSmallScreen
                      ? "150px"
                      : "290px",
                    marginLeft: isMediumScreen
                      ? "220px"
                      : isSmallScreen
                      ? "150px"
                      : "20px",
                    position: "absolute",
                    marginLeft: "0",
                    left: "51%",
                    transform:
                      isSmallScreen || isMediumScreen
                        ? "translateX(-50%)"
                        : "translateX(0)",
                  }}
                >
                  <Typography
                    variant="h4"
                    color="primary"
                    style={{
                      fontWeight: "bold",
                      color: "white",
                      textAlign: "left",
                      fontSize: "24px",
                    }}
                  >
                    Because
                    <br />
                    <span
                      style={{
                        fontWeight: "normal",
                        fontSize: 24,
                        color: "white",
                      }}
                    >
                      your voice matters
                    </span>
                  </Typography>
                  <img src="/Images/scan-barcode.png"></img>
                </Box>
              </Grid>
            </Grid>
            <Typography
              variant="h6"
              style={{
                fontWeight: 600,
                color: "white",
                textAlign: "center",
                fontSize: "18px",

                bottom: isMediumScreen5 ? "10px" : "20px", // Adjust the bottom position
                width: "100%",
              }}
              sx={{ marginTop: { xs: "420px", sm: "380px", md: "50px" } }}
            >
              Human Resource Department
            </Typography>
          </DialogContent>
        </Dialog>
      </div>
    </ThemeProvider>
  );
};

export default SlideInPopup;
