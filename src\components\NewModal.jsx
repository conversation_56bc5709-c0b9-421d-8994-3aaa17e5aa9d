import React from 'react';
import {
  Dialog,
  DialogContent,
  Box,
  Typography,
  Icon,
} from '@mui/material';
import EmojiEmotionsIcon from '@mui/icons-material/EmojiEmotions';
import StarIcon from '@mui/icons-material/Star';

export default function ThankYouModal({ open, onClose }) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          borderRadius: 4,
          textAlign: 'center',
          p: 3,
          minWidth: 350,
        }
      }}
    >
      <DialogContent>
        {/* Icon */}
        <Box sx={{ mb: 2 }}>
        <img src="../images/star.png" alt="Rate" style={{ height: 30 }} />
        </Box>

        {/* Heading */}
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#a66bcb', mb: 1 }}>
          Thank You!
        </Typography>

        {/* Subheading */}
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          You’ve made someone’s day brighter!
        </Typography>

        {/* Description */}
        <Typography variant="body2" sx={{ color: '#999' }}>
          A small gesture of appreciation can bring a big<br />
          smile and uplift someone`&apos;s spirit! <EmojiEmotionsIcon fontSize="small" sx={{ color: '#f4c542' }} />
        </Typography>
      </DialogContent>
    </Dialog>
  );
}
