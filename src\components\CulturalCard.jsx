"use client"
import { Box, But<PERSON>, Divider, Typography } from "@mui/material";
import React, { useState } from "react";
import "@fontsource/urbanist";
import "@fontsource/inter";

const CulturalCard = () => {
 
  const images = [
    "/Ellipse 80.png",
    "/Ellipse 81.png",
    "/Ellipse 82.png",
    
  ];
  

  return (
    <>
      <Box
        sx={{
          width: "100%",
          maxWidth: "392px",
          height: "171px",
          backgroundColor: "white",
          boxShadow: "0 8px 15px rgba(0, 0, 0, 0.1)",
          px: "26px",
          py: "18px",
          borderRadius: "10px",
        }}
      >
        <Box
          sx={{
            maxWidth: "340px",
            height: "134px",
          }}
        >
          <Typography
            sx={{
              marginBottom: "13px",
              fontFamily: "Urbanist",
              fontSize: "20px",
              fontWeight: 800,
              color: "#1b3745",
              lineHeight: 0.7,
            }}
          >
           Cultural Ambassadors
          </Typography>
          <Typography
            sx={{
              fontFamily: "Inter",
              fontWeight: 400,
              fontSize: "12px",
              color: "#b0b0b0",
            }}
          >
            Welcome <PERSON>a and our new team members! We`&apos;re excited to start
            this journey together
          </Typography>
          <Divider
            sx={{
              width: "90%",
              borderBottomStyle: "dashed",
              borderColor: "#1B37450F",
              borderBottomWidth: "1px",
              my: 2,
            }}
          />
          <Box sx={{ 
            display: "flex", 
            gap: 1,  
            width: '277px', 
}}>
        {images.map((src, index) => (
          <Box
            key={index}
            component="img"
            src={src}  
            alt={`user-${index}`}
            sx={{
              width: "40px",
              height: "40px",
              borderRadius: "50%",
             }}/>
        ))}
        
      </Box>
        </Box>
      </Box>
    </>
  );
};

export default CulturalCard;
