import Dashboard from "@/components/Dashboard";
import * as React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  Al<PERSON>,
} from "@mui/material";
import { useColor } from "@/components/ColorContext";
import { useSelectedColor } from "@/components/HelperFunctions";
import { useTheme } from "@mui/material/styles";
import { useMediaQuery } from "@mui/material";
import SideCard from "@/components/SideCard";
import WalaOfferCard from "@/components/WalaOfferCard";
import { useSession } from "next-auth/react";
import { useQuery } from "@apollo/client";
import { GET_OFFERS } from "../Data/Offer";

export default function HassanaOffers() {
  const [offers, setOffers] = React.useState([]);
  const [selectedCard, setSelectedCard] = React.useState(null);
  const [open, setOpen] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState(null);
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { data: session, status } = useSession();

  // Fetch offers using GET_OFFERS query
  const { data, loading, error, refetch } = useQuery(GET_OFFERS, {
    variables: { user_id: session?.user?.id || "" },
    skip: !session?.user?.id,
    context: {
      headers: {
        Authorization: `Bearer ${session?.accessToken || localStorage.getItem("jwtToken") || ""}`,
      },
    },
    onError: (err) => {
      console.error("Error fetching offers:", JSON.stringify(err, null, 2));
      const message =
        err.graphQLErrors?.[0]?.message ||
        err.networkError?.result?.errors?.[0]?.message ||
        err.message ||
        "Failed to fetch offers";
      if (message.includes("Unauthorized") || message.includes("Forbidden")) {
        setErrorMessage("Authentication failed. Please log in again.");
      } else {
        setErrorMessage(message);
      }
    },
  });

  // Process fetched offers
  React.useEffect(() => {
    if (data && data.offers) {
      const fetchedOffers = data.offers.map((offer) => ({
        id: offer.id,
        title: offer.title || offer.name || "Untitled Offer", // Fallback if title is missing
        name: offer.name || "Unknown",
        code: offer.code || "N/A",
        contactInfo: offer.contact_information || "N/A",
        entryDate: offer.expiry_date
          ? new Date(offer.expiry_date).toLocaleString()
          : "N/A",
        description: offer.description || "No description provided.",
      }));
      setOffers(fetchedOffers);
    }
  }, [data]);

  const handleCardClick = (card) => {
    setSelectedCard(card);
    if (isSmallScreen) {
      setOpen(true); // Open modal on small screens
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  if (status === "loading") {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Typography variant="h6" textAlign="center">
            Loading session...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Grid
        container
        spacing={2}
        sx={{
          padding: "10px",
          mt: 3,
          backgroundColor: theme.palette.background.secondary,
          flexDirection: "row",
          height: "90vh",
        }}
      >
        {/* Left Side: Offer Cards */}
        <Grid item xs={12} sm={6} sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {loading ? (
            <Typography>Loading offers...</Typography>
          ) : errorMessage ? (
            <Alert severity="error" onClose={() => setErrorMessage(null)}>
              {errorMessage}
            </Alert>
          ) : offers.length === 0 ? (
            <Typography>No offers available.</Typography>
          ) : (
            offers.map((offer, index) => (
              <Card
                key={offer.id || index}
                onClick={() => handleCardClick(offer)}
                sx={{
                  cursor: "default",
                  backgroundColor:
                    selectedColor === theme.palette.background.primary
                      ? theme.palette.background.secondary
                      : selectedColor,
                  color:
                    selectedColor === theme.palette.background.primary
                      ? theme.palette.text.primary
                      : theme.palette.text.white,
                  padding: "10px",
                }}
              >
                <CardContent>
                  <Typography variant="h5">{offer.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {offer.description.substring(0, 50)}...
                  </Typography>
                </CardContent>
              </Card>
            ))
          )}
        </Grid>

        {/* Right Side */}
        <Grid item xs={12} sm={6} sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {/* <SideCard /> */}
          {!isSmallScreen && selectedCard && (
            <Card
              sx={{
                position: "relative",
                backgroundColor:
                  selectedColor === theme.palette.background.primary
                    ? theme.palette.background.secondary
                    : selectedColor,
                color:
                  selectedColor === theme.palette.background.primary
                    ? theme.palette.text.primary
                    : theme.palette.text.white,
                padding: "10px",
                borderRadius: 3,
                overflow: "hidden",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "5px",
                  height: "100%",
                  background: "linear-gradient(to bottom, #a368e2, #66b1f2)",
                  borderTopLeftRadius: 12,
                  borderBottomLeftRadius: 12,
                },
              }}
            >
              <CardContent>
                <Typography
                  variant="subtitle1"
                  gutterBottom
                  sx={{ color: "#2c3e50", fontWeight: 600, mb: 2 }}
                >
                  <Box
                    component="span"
                    sx={{
                      borderBottom: "3px solid #9C71E4",
                      display: "inline-block",
                      pb: "2px",
                    }}
                  >
                    {selectedCard.title}
                  </Box>
                </Typography>
                <Grid container spacing={15} mb={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ color: "#b0b0b0" }}>
                      Name:
                    </Typography>
                    <Typography fontWeight="bold" sx={{ color: "#2c3e50" }}>
                      {selectedCard.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ color: "#b0b0b0" }}>
                      Code
                    </Typography>
                    <Typography fontWeight="bold" sx={{ color: "#2c3e50" }}>
                      {selectedCard.code}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container spacing={15} mb={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ color: "#b0b0b0" }}>
                      Contact Information
                    </Typography>
                    <Typography fontWeight="bold" sx={{ color: "#2c3e50" }}>
                      {selectedCard.contactInfo}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ color: "#b0b0b0" }}>
                      Expiry Date
                    </Typography>
                    <Typography fontWeight="bold" sx={{ color: "#2c3e50" }}>
                      {selectedCard.entryDate}
                    </Typography>
                  </Grid>
                </Grid>
                <Typography variant="body2" sx={{ color: "#b0b0b0" }} gutterBottom>
                  Description
                </Typography>
                <Typography fontWeight="bold" sx={{ color: "#2c3e50" }}>
                  {selectedCard.description}
                </Typography>
              </CardContent>
            </Card>
          )}
          <WalaOfferCard />
        </Grid>

        {/* Modal for Smaller Screens */}
        <Dialog open={open} onClose={handleClose} fullWidth>
          <DialogTitle>{selectedCard?.title}</DialogTitle>
          <DialogContent>
            <Typography>
              <strong>Name:</strong> {selectedCard?.name}
            </Typography>
            <Typography>
              <strong>Code:</strong> {selectedCard?.code}
            </Typography>
            <Typography>
              <strong>Contact Info:</strong> {selectedCard?.contactInfo}
            </Typography>
            <Typography>
              <strong>Entry Date:</strong> {selectedCard?.entryDate}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Description:</strong> {selectedCard?.description}
            </Typography>
          </DialogContent>
        </Dialog>
      </Grid>
    </Dashboard>
  );
}