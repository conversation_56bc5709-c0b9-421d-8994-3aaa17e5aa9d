import Dashboard from "@/components/Dashboard";
import {
  Box,
  Button,
  CircularProgress,
  MenuItem,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import DataTable from "./component/DataTable";
import { useMutation, useQuery } from "@apollo/client";
import { useEffect, useState } from "react";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import {
  getQuotes,
  mutationCreateQuote,
  mutationRemoveQuote,
  mutationUpdateQuote,
} from "@/Data/Quote";
import { getDate } from "@/components/HelperFunctions";
import withAuth from "@/components/auth/withAuth";
import {
  getLeaves,
  mutationCreateLeave,
  mutationRemoveLeave,
  mutationUpdateLeave,
} from "@/Data/Leave";
import { getUsersInstance } from "@/Data/User";

const columns = [
  { id: "username", label: "Username", minWidth: 100 },
  // { id: "user_id", label: "Employee", minWidth: 100, align: "center" },
  { id: "remarks", label: "Remarks", minWidth: 250 },
  { id: "typeOfLeave", label: "type", minWidth: 100, align: "center" },
  { id: "numberOfDays", label: "Days", minWidth: 100, align: "center" },
  { id: "date", label: "Date", minWidth: 100, align: "center" },
];

const AddLeaves = (props) => {
  const {
    data,
    setOpenUpdate,
    opt,
    leaves,
    setLeaves,
    setOpen,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
  } = props;
  const [id, setId] = useState(data && opt == "update" ? data.id : "");
  const [numberOfDays, setNumberOfDays] = useState(
    data && data.numberOfDays ? data.numberOfDays : ""
  );
  const [leaveType, setLeaveType] = useState(data ? data.typeOfLeave : "");
  const [user_id, setuser_id] = useState(data ? data.user_id : "");
  const [username, setUsername] = useState(data ? data.username : "");
  const [users, setUsers] = useState();
  const [remarks, setRemarks] = useState(data ? data.remarks : "");
  const [date, setDate] = useState(
    data && opt == "update" ? data.date : dayjs()
  );
  const [updateLeave] = useMutation(mutationUpdateLeave);
  const [createLeave] = useMutation(mutationCreateLeave);

  console.log("data", data);

  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
  } = useQuery(getUsersInstance);

  const theme = useTheme();

  const [errors, setErrors] = useState({});
  const validateForm = () => {
    let tempErrors = {};
    if (!numberOfDays) tempErrors.numberOfDays = "numberOfDays is required.";
    if (!remarks) tempErrors.remarks = "remarks is required.";
    if (!user_id) tempErrors.user_id = "user_id is required.";
    if (!date) tempErrors.date = "dateis required.";
    if (!leaveType) tempErrors.leaveType = "leaveType required.";
    // if (!excerpt) tempErrors.excerpt = "excerpt is required.";
    // ... other validations
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const submitHandler = () => {
    return new Promise(async (res, rej) => {
      if (validateForm()) {
        let Data = {
          variables: {
            id: id,
            user_id: parseInt(user_id),
            username: username,
            remarks: remarks,
            numberOfDays: parseInt(numberOfDays),
            typeOfLeave: leaveType,
            date: getDate(date),
          },
        };
        console.log("date", Data);
        opt != "update"
          ? await createLeave(Data)
              .then((response) => {
                setLeaves([...leaves, response.data.createLeave]);
                setOpen(false);
                setSnackbarMessage(`Leave added successfully`);
                setSnackbarSeverity("success");
                setSnackbarOpen(true);
              })
              .catch((error) => {
                console.log(error);
                rej(error);
                setSnackbarMessage(`Failed to add Leave`);
                setSnackbarSeverity("error");
                setSnackbarOpen(true);
              })
          : await updateLeave(Data)
              .then((response) => {
                const itemIndex = leaves.findIndex(
                  (item) => item.id === Data.variables.id
                );
                const quote = response.data.updateLeave;
                if (itemIndex !== -1) {
                  const updatedData = [...leaves];
                  console.log("in itemIndex", response.data.updateLeave);
                  updatedData[itemIndex] = {
                    id,
                    user_id: parseInt(user_id),
                    username,
                    remarks,
                    numberOfDays: parseInt(numberOfDays),
                    typeOfLeave: leaveType,
                    date: getDate(date),
                  };
                  //  s(updatedData);
                  setOpenUpdate(false);
                  // setSnackbarMessage(`Quote updated successfully`);
                  // setSnackbarSeverity("success");
                  // setSnackbarOpen(true);
                }
              })
              .catch((error) => {
                console.log(error);
                rej(error);
                // setSnackbarMessage(`Failed to update Quote`);
                // setSnackbarSeverity("error");
                // setSnackbarOpen(true);
              });
        res(Data);
      }
    });
  };
  //   user_id
  //   username
  //   date
  //   numberOfDays
  //   typeOfLeave
  return queryLoading ? (
    <Box sx={{ display: "flex", justifyContent: "center" }}>
      <CircularProgress color="secondary" />
    </Box>
  ) : (
    <>
      <TextField
        id="user"
        select
        label="Employee"
        // defaultValue="true"
        error={!!errors.user_id}
        helperText={errors.user_id}
        fullWidth
        // helperText="Please select user"
        value={user_id + "|" + username}
        margin="normal"
        onChange={(e) => {
          setuser_id(e.target.value.split("|")[0]);
          setUsername(e.target.value.split("|")[1]);
        }}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        {queryData &&
          queryData.users.map((user, index) => (
            <MenuItem value={user.id + "|" + user.name} key={index}>
              {user.name}
            </MenuItem>
          ))}
      </TextField>
      {/* <TextField
                margin="normal"
                size="small"
                id="numberOfDays"
                error={!!errors.numberOfDays}
                helperText={errors.numberOfDays}
                fullWidth
                label="Number Of Leaves"
                value={numberOfDays}
                onChange={(e) => setNumberOfDays(e.target.value)}
            /> */}
      <TextField
        margin="normal"
        size="small"
        id="numberOfDays"
        error={!!errors.numberOfDays}
        helperText={errors.numberOfDays}
        fullWidth
        label="Number Of Days"
        value={numberOfDays}
        onChange={(e) => {
          // Allow only numeric input
          const value = e.target.value;
          const re = /^[0-9\b]+$/; // Regular expression to allow numbers and backspace

          // If the value is not blank and is a number, update the state
          if (value === "" || re.test(value)) {
            setNumberOfDays(value);
          }
        }}
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          id="date"
          label="Date"
          value={opt != "update" ? date : dayjs(date)}
          onChange={setDate}
          error={!!errors.date}
          helperText={errors.date}
          referenceDate={dayjs("2022-04-17")}
          defaultValue={opt != "update" && dayjs()}
          minDate={dayjs()}
          sx={{ width: "100%", marginY: "10px" }}
        />
      </LocalizationProvider>
      <TextField
        id="typeOfLeave"
        select
        label="Leave Type"
        // defaultValue="true"
        error={!!errors.leaveType}
        helperText={errors.leaveType}
        fullWidth
        // helperText="Please select user"
        value={leaveType}
        margin="normal"
        onChange={(e) => setLeaveType(e.target.value)}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        <MenuItem value="casual">Annual</MenuItem>
        <MenuItem value="medical">Sick</MenuItem>
      </TextField>

      <TextField
        margin="normal"
        size="small"
        id="remarks"
        label="Remarks"
        error={!!errors.remarks}
        helperText={errors.remarks}
        multiline
        rows={6}
        placeholder="..."
        fullWidth
        value={remarks}
        onChange={(e) => setRemarks(e.target.value)}
      />

      {/* <Button onClick={updateHandler}>Update</Button> */}
      <Button
        onClick={submitHandler}
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt != "update" ? "Submit" : "Update"}
      </Button>
    </>
  );
};
const Leaves = () => {
  const [open, setOpen] = useState(false);
  const [Data, setData] = useState(null);
  const [leaves, setLeaves] = useState([]);
  const [openUpdateBox, setOpenUpdateBox] = useState(false);
  const [index, setIndex] = useState(null);
  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
  } = useQuery(getLeaves);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [removeLeave] = useMutation(mutationRemoveLeave);

  const deleteHandler = async (deletedId) => {
    try {
      await removeLeave({ variables: { id: deletedId } });

      const updatedQuotes = leaves.filter((quote) => quote.id !== deletedId);
      setLeaves(updatedQuotes);

      setSnackbarMessage("Deleted successfully");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error(error);

      setSnackbarMessage("Deletion failed");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  useEffect(() => {
    if (!queryLoading && !queryError && queryData && queryData.leaves) {
      const leaveData = queryData.leaves;
      console.log(leaveData);
      setLeaves(leaveData);
    }
  }, [queryLoading, queryError, queryData]);

  return (
    <>
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          {/* <Typography variant="h5" sx={{ marginY: "10px" }}>Quotes</Typography> */}
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography variant="h5" sx={{ marginY: "10px" }}>
              Leaves
            </Typography>
            <BasicModal
              title={"Add Leave"}
              comp={
                <AddLeaves
                  // data={data}
                  // setData={setData}
                  opt={"add"}
                  leaves={leaves}
                  setLeaves={setLeaves}
                  setOpen={setOpen}
                  // setOpenUpdate={setOpenUpdate}
                  setSnackbarMessage={setSnackbarMessage}
                  setSnackbarSeverity={setSnackbarSeverity}
                  setSnackbarOpen={setSnackbarOpen}
                />
              }
              btn={true}
              open={open}
              setOpen={setOpen}
            />
          </Box>
          <UpdateModal
            title={"Update Leave"}
            comp={
              <AddLeaves
                data={Data}
                setOpenUpdate={setOpenUpdateBox}
                leaves={leaves}
                opt={"update"}
                setLeaves={setLeaves}
              />
            }
            btn={false}
            openUpdate={openUpdateBox}
            setOpenUpdate={setOpenUpdateBox}
          />
          {queryLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress color="secondary" />
            </Box>
          ) : (
            <>
              {leaves.length > 0 ? (
                <Box sx={{ maxWidth: "100%", overflow: "auto" }}>
                  <DataTable
                    columns={columns}
                    rows={leaves}
                    setData={setData}
                    setOpen={setOpenUpdateBox}
                    updateKey={"username"}
                    setIndex={setIndex}
                    action={true}
                    deleteHandler={deleteHandler}
                  />
                </Box>
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Typography variant="h5" sx={{ marginY: "10px" }}>
                    No data found
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
      </Dashboard>
    </>
  );
};
export default withAuth(Leaves);
