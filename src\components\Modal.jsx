import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { Dialog, Grid, TextField, useTheme } from '@mui/material';
import Image from 'next/image';


export default function BasicModal({ comp, Data,
    dataModal,
    setDataModal, btn }) {
    const [open, setOpen] = React.useState(false);
    const handleOpen = () => { setOpen(true); setDataModal(true); };
    const handleClose = () => { setOpen(false); setDataModal(false); };
    const theme = useTheme();
    const style = {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: "60%",
        background: theme.palette.background.primary,
        // border: '2px solid #000',
        borderRadius: "20px",
        boxShadow: 24,
        height: "600px",
        overflow: "auto",
        // p: 4,
    };
    return (
        <div>
            {btn && <Button onClick={handleOpen}>Open modal</Button>}
            <Modal
                open={open || dataModal}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box>
                    <Box sx={style}>
                        <Box
                            style={{
                                backgroundColor: "#003e53",
                                display: "flex",
                                alignItems: "center",
                                borderTopRightRadius: "10px",
                                borderTopLeftRadius: "10px",
                                justifyContent: "space-between",
                                // marginTop: "10px",
                                padding: "15px",
                                borderTop: "3px solid #b484cc",
                                borderBottom: "3px solid #b484cc",
                            }}
                        >
                            <Image
                                src="/images/HassanaLogos.png"
                                alt="hassana"
                                width={150}
                                height={150}
                            />
                        </Box>
                        <Box sx={{p: 4}}>
                            <Typography id="modal-modal-title" variant="h4">
                                {Data && Data.title}
                            </Typography>
                            <Box
                                sx={{
                                    height: "2px",
                                    width: "30%",
                                    background: theme.palette.text.green,
                                    marginY: "5px",
                                    // marginX: "auto"
                                    // position: 'absolute', top: '100px', left: '35%'
                                }}
                            />
                            <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                                {Data && Data.details}
                            </Typography>
                        </Box>
                    </Box>
                </Box>
            </Modal>

{/* <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Use Google's location service?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Let Google help apps determine location. This means sending anonymous
            location data to Google, even when no apps are running.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Disagree</Button>
          <Button onClick={handleClose} autoFocus>
            Agree
          </Button>
        </DialogActions>
      </Dialog> */}
        </div>
    );
}