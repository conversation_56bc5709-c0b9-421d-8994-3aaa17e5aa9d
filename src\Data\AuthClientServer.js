import { ApolloClient, InMemoryCache, createHttpLink } from "@apollo/client";
import { baseUrl } from "./ApolloClient";

 //export const baseUrl = "http://10.0.1.16:3001";
//export const baseUrl = "https://portal.hassana.com.sa/v1";
// export const baseUrl = "http://213.199.34.84:3001";

const httpLink = createHttpLink({
  uri: baseUrl + "/graphql",
});

const authclient = new ApolloClient({
  link: httpLink,
  cache: new InMemoryCache(),

  onError: ({ operation, networkError, response }) => {
    console.log(
      `[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `,
      response
    );
  },
});
authclient.resetCache = async () => {
  await authclient.cache.reset();
  console.log("Apollo Client cache has been reset.");
};
export default authclient;
