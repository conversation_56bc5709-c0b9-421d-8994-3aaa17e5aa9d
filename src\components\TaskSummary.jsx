import {
  Avatar,
  AvatarGroup,
  Box,
  Grid,
  IconButton,
  Typography,
  useTheme,
} from "@mui/material";
import MailOutline from "@mui/icons-material/MailOutline";
import Image from 'next/image';
import CircularWithValueLabel from "./CircularWithValueLabel";

const TaskSummary = () => {
  const theme = useTheme();
  return (
    <>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: "10px",
          boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
          padding: "20px",
        }}
      >
        <Typography variant="h6" component="h2">
          Task Summary
        </Typography>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <Box>
              <Typography variant="body1" component="h5" color={"#BBB"}>
                Team Member
              </Typography>
              <Box sx={{ display: "inline-block" }}>
                <AvatarGroup
                  max={4}
                  total={6}
                  spacing={15}
                  sx={{ float: "left", marginTop: "20px" }}
                >
                  <Avatar alt="Remy Sharp" src="/images/avatars/1.png" />
                  <Avatar alt="Remy Sharp" src="/images/avatars/2.png" />
                  <Avatar alt="Remy Sharp" src="/images/avatars/3.png" />
                  <Avatar alt="Remy Sharp" src="/images/avatars/4.png" />
                </AvatarGroup>
              </Box>
              <Box>
                <IconButton>
                  <Image src="/icons/sms.png" alt="sms" />
                </IconButton>
                <IconButton>
                  <Image src="/icons/star.png" alt="star" />
                </IconButton>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body1" component="h5" textAlign={"center"}>
              Task Completed
            </Typography>
            <Box sx={{ textAlign: "center" }}>
              <Box>
                <CircularWithValueLabel />
              </Box>
            </Box>
            <Typography
              variant="caption"
              color={"#BBB"}
              component="h5"
              sx={{ textAlign: "center" }}
            >
              Task Done 34 / 50
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default TaskSummary;
