import React from "react";
import { Typography, Box, Divider } from "@mui/material";
import { useMediaQuery, useTheme } from "@mui/material";
import { getNotifications } from "@/Data/Notification";

const NotificationCard = ({
  notification,
  isSelected,
  onClick,
  isCurrentNotification,
  showDate,
  date,
}) => {
  const isMediumScreen = useMediaQuery("(max-width: 1200px)");
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: isMediumScreen ? "center" : "flex-start",
        gap: "15px",
        width: "100%",
        paddingLeft: "10px",
        //padding: "16px",
        // backgroundColor: isCurrentNotification 
        //   ? theme.palette.background.secondary 
        //   : theme.palette.background.paper,
        // borderRadius: "8px",
        // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        cursor: "pointer",
        marginBottom: "16px",
        // "&:hover": {
        //   backgroundColor: theme.palette.action.hover,
        // },
      }}
      onClick={onClick}
    >
      <Box sx={{ width: "100%" }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            fontSize: "16px",
            fontFamily: "Urbanist",
            color: theme.palette.text.primary,
          
          }}
        >
          
          {notification}
        </Typography>
        <Divider
          sx={{
            width: "122px",
            border: isCurrentNotification
              ? "1px solid #A665E1"
              : "1px solid #00BC82",
            my: 1,
          }}
        />
        { date && (
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: "14px",
            }}
          >
            {date}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default NotificationCard;