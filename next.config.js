// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   reactStrictMode: true,
//   images: {
//     domains: ['localhost','***************'],
//   },
// }

// module.exports = nextConfig
/** @type {import('next').NextConfig} */  
const nextConfig = {  
  reactStrictMode: true,  
  images: {  
    remotePatterns: [  
      {  
        protocol: 'http', // Ya 'https', jo bhi aapki configuration ho  
        hostname: 'localhost',  
        port: '', // Ye khaali chhod sakte hain agar port specify nahi hai  
        pathname: '/**', // This allows any path, adjust as needed  
      },  
      {  
        protocol: 'http', // Ya 'https'  
        hostname: '***************',  
        port: '', // Ye khaali chhod sakte hain agar port specify nahi hai  
        pathname: '/**', // This allows any path, adjust as needed  
      },  
    ],  
  },  
};  

module.exports = nextConfig;
