import * as React from "react";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Typography from "@mui/material/Typography";
import { Box } from "@mui/material";
import profileData from "../profileData.json";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { baseUrl } from "../Data/ApolloClient";
import axios from "axios";

const Profile = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const base_url = baseUrl;
  const users = "v1/app-users";
  
  const [usersData, setUsersData] = React.useState(null);
  const [matchedUser, setMatchedUser] = React.useState(null);
  const imgUrl = base_url+/v1/+matchedUser?.profile

  // Find matching user whenever usersData or session changes
  React.useEffect(() => {
    if (usersData?.data && session?.user?.id) {
      const foundUser = usersData.data.find(user => user.id === session.user.id);
      setMatchedUser(foundUser || null);
      console.log("pppp",foundUser);
      
    }
  }, [usersData, session?.user?.id]);


  const getAllUsers = async (page = 1, pageSize = 100) => {
    if (!session?.accessToken) return;
    
    try {
      const response = await axios.get(`${base_url}/${users}`, {
        params: { page, pageSize },
        headers: { Authorization: `Bearer ${session.accessToken}` },
      });
      setUsersData(response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching users:", error);
      return { error: error.message, status: error.response?.status };
    }
  };

  React.useEffect(() => {
    getAllUsers();
  }, [session?.accessToken]);

  const handleProfileClick = () => {
    router.push("../../myprofile");
  };

  if (!session) {
    return <div>Loading...</div>;
  }

  // Use matchedUser data if available, otherwise fall back to session or profileData
  const userProfile = matchedUser || session?.user || profileData;

  return (
    <ListItemButton
      sx={{ paddingLeft: "0.2px", paddingBottom: "20px" }}
      onClick={handleProfileClick}
    >
      <ListItemIcon>
        <Box>
          <img
            src={userProfile?.profile && imgUrl || '/images/avatars/1.png'}
            alt="Profile Icon"
            width={50}
            height={50}
            style={{ borderRadius: "20%", objectFit: "cover", width: 40, height: 50}}
          />
        </Box>
      </ListItemIcon>
      <Box sx={{ marginLeft: "15px" }}>
        <ListItemText
          primary={
            (userProfile?.name &&
              userProfile.name.split("@")[0].replace(".", " ")) ||
            profileData.name
          }
          sx={{
            "& .MuiListItemText-primary": {
              fontWeight: "bold",
              fontSize: 20,
            },
          }}
        />
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Typography variant="body2" color="textSecondary">
            {userProfile?.role}
          </Typography>
        </Box>
      </Box>
    </ListItemButton>
  );
};

export default Profile;