import React, { useEffect, useState } from "react";
import { signIn, useSession } from "next-auth/react";
import { useRouter } from "next/router";
import CircularProgress from "@mui/material/CircularProgress";

const withAuth = (WrappedComponent) => {
  return function ProtectedComponent(props) {
    const { data: session, status } = useSession();
    const router = useRouter();
    const [isReady, setReady] = useState(false);

    useEffect(() => {
      if (status === "loading") return;
      if (status === "unauthenticated") {
        router.push("/login?login=false");
      } else {
        setReady(true); // Set ready when authenticated
      }
    }, [status, router]);

    if (status === "loading" || !isReady) {
      return (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress color="secondary" />
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

export default withAuth;
