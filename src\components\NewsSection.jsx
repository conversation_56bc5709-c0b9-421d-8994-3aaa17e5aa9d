import React from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Link from "next/link";
import { formatDateTimeUTC } from "./HelperFunctions";
import { useQuery } from "@apollo/client";
import { getResources } from "@/Data/Resource";
import { getAllNews } from "@/Data/News";

const NewsSection = ({
  isWideScreen,
  isLargeTablet,
  isSmallTablet,
  isMediumMobile,
  news,
}) => {
  // Function to limit summary text
  const limitSummary = (summary) => {
    const words = summary.split(" ");
    if (words.length > 200) {
      return words.slice(0, 200).join(" ") + "...";
    }
    return summary;
  };

  // Fetch resources using useQuery
  const { data, loading, error } = useQuery(getResources);

  // Construct featuredImage URL based on news prop
  //const baseUrl = "http://localhost:3001/v1/resource/v1/news/";
  const featuredImage = news && news.featuredImage
   

  return (
    <>
      {news && (
        <Box
          style={{
            display: "flex",
            alignItems: "center",
            marginLeft: isSmallTablet ? "15px" : "32px",
            marginTop: "20px",
            flexDirection: isMediumMobile ? "column" : "row",
            gap: isWideScreen ? "10px" : "0",
          }}
        >
          {loading ? (
            <Typography style={{ marginRight: "16px" }}>Loading image...</Typography>
          ) : error ? (
            <Typography style={{ marginRight: "16px", color: "red" }}>
              Failed to load image: {error.message}
            </Typography>
          ) : featuredImage ? (
            <img
              src={news.featuredImage.replace(
                      "http://localhost:3009",
                      "https://hassana-api.360xpertsolutions.com"
                    )}
              style={{
                marginRight: "16px",
                maxWidth: isMediumMobile
                  ? "100%"
                  : isLargeTablet
                  ? "45%"
                  : "50%",
                maxHeight: "50%",
                width: "50%",
              }}
              alt="Featured News Image"
              onError={(e) => console.log("Image failed to load:", featuredImage)}
            />
          ) : (
            <Typography style={{ marginRight: "16px" }}>
              No image available
            </Typography>
          )}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography style={{ color: "#00BC82", marginBottom: "8px" }}>
              {formatDateTimeUTC(news.publication)}
            </Typography>
            <Typography
              style={{
                fontSize: "14px",
                fontWeight: "500",
                marginBottom: "8px",
              }}
            >
              {limitSummary(news.title)}
            </Typography>
            <Link href={news.url} target="_blank">
              <Button
                variant="contained"
                style={{
                  borderRadius: 5,
                  padding: "7.005px 17.514px",
                  background: "#62B6F3",
                  color: "#fff",
                  fontSize: "12.259px",
                  fontFamily: "Urbanist",
                  maxWidth: "150px",
                  width: "100%",
                }}
              >
                Learn More
              </Button>
            </Link>
          </div>
        </Box>
      )}
      <Box
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-around",
          padding: "10px",
        }}
      >
        <Button
          sx={{
            fontSize: {
              lg: "10px",
              md: "8px",
              sm: "8px",
              xs: "7px",
              xl: "12px",
            },
          }}
        >
          All News
        </Button>
        <Button
          sx={{
            fontSize: {
              lg: "10px",
              md: "8px",
              sm: "8px",
              xs: "7px",
              xl: "12px",
            },
          }}
        >
          Events
        </Button>
        <Button
          sx={{
            fontSize: {
              lg: "10px",
              md: "8px",
              sm: "8px",
              xs: "7px",
              xl: "12px",
            },
          }}
        >
          Discussion
        </Button>
        <div
          className="Line8"
          style={{
            width: "50%",
            height: "0",
            border: "1px #00BC82 solid",
            maxWidth: "572px",
          }}
        ></div>
      </Box>
    </>
  );
};

export default NewsSection;