// File: AnnouncementItem.js
import React from "react";
// import ResponsiveBox from './ResponsiveBox'; // Adjust the path accordingly
import AnnouncementCard from "@/components/AnnouncementCard";
import { useTheme } from "@mui/material/styles";

const PreviousAnnouncementItem = ({
  announcement,
  handleOpenModal,
  ResponsiveBox,
  isCurrentAnnouncement,
}) => {
  const theme = useTheme();
  return (
    <ResponsiveBox
      sx={{
        padding: "30.41px 16.91px 29.04px 16.91px",
        marginTop: "20px",
        cursor: "pointer",
      }}
      isCurrentAnnouncement={isCurrentAnnouncement}
    >
      <AnnouncementCard
        title={announcement.title}
        details={announcement.details}
        onClick={() => handleOpenModal(announcement)}
        isCurrentAnnouncement={isCurrentAnnouncement}
        image={announcement.image}
      />
    </ResponsiveBox>
  );
};

export default PreviousAnnouncementItem;
