// import { gql } from "@apollo/client";
import axios from "axios";
import formData from 'form-data';
import { baseUrl } from "./ApolloClient";

let base_url = baseUrl;

let news = '/v1/our-news';

export const getAllNews = async () => {
  try {
    console.log("Requesting URL:", `${base_url}${news}`); 

    const response = await axios.get(`${base_url}${news}`,);

    console.log("getAllNews response:", response.data); 
    return response.data.data
  } catch (error) {
    console.error("getAllNews error:", error);
    return []; // Return empty array on error
  }
};
export const getInternalNews = async () => {
  try {
    let response = await axios.get(`${base_url}${news}/internal-news`)
    console.log("ooooooo",response);
    return response ? response.data : null;


  } catch (error) {
    console.log(error);
    return error.message
  }
}
export const getExternalNews = async () => {
  try {
    let response = await axios.get(`${base_url}${news}/external-news`)
    console.log("external",response);
    return response ? response.data : null;


  } catch (error) {
    console.log(error);
    return error.message
  }
}

export const updateNews = async (FormData, isImageChanged,token) => {
  try {
    let data = new formData();
    console.log(isImageChanged);
    if (isImageChanged) {
      data.append('featuredImage', FormData.featuredImage)
    }
    data.append('title', FormData.title);
    data.append('url', FormData.url);
    data.append('category', FormData.category);
    data.append('summary', FormData.summary);
    data.append('author', FormData.author);
    data.append('source', FormData.source);
    data.append('status', FormData.status);
    data.append('visibility', FormData.visibility);
    // data.append('publication', Date.now());
    // data.append('created_on', Date.now());
    data.append('created_by', 'Admin');
    // data.append('updated_on', Date.now());
    data.append('updated_by', 'Admin');

    let config = {
      method: 'patch',
      maxBodyLength: Infinity,
      url: `${base_url}${news}/${FormData.id}`,
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${token}`,
      },
      data: data
    };

    let res = await axios.request(config);
    console.log(res, "jhdgfjs");
    return res.data;

  } catch (error) {
    console.log(error);
    return error.message
  }
}

export const createNews = async (FormData) => {
  try {
    let data = new formData();

    data.append('title', FormData.title);
    data.append('url', FormData.url);
    data.append('category', FormData.category);
    data.append('featuredImage', FormData.featuredImage);
    data.append('summary', FormData.summary);
    data.append('author', FormData.author);
    data.append('source', FormData.source);
    data.append('status', FormData.status);
    data.append('visibility', FormData.visibility);
    data.append('publication', new Date().toISOString());
    data.append('created_on', new Date().toISOString());
    data.append('created_by', 'Admin');
    data.append('updated_on', new Date().toISOString());
    data.append('updated_by', 'Admin');

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${base_url}${news}`,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: data
    };

    let res = await axios.request(config);
    console.log(res.data);
    return res.data;

  } catch (error) {
    console.log(error);
    return error.message
  }
}

export const deleteNews = async (id) => {
  let config = {
    method: 'delete',
    maxBodyLength: Infinity,
    url: `${base_url}${news}/${id}`,
    headers: {}
  };

  let res = await axios.request(config);
  console.log(res.data);
  return res.data;
}