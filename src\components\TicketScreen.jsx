import { useState, useEffect, useContext, useRef } from "react";
import { styled } from "@mui/system";
import { Box, Grid, Typography, Button, CircularProgress } from "@mui/material";
import AnnouncementCard from "@/components/OfficeBoyCards";
import AnnouncementDialog from "@/components/AnnouncementDialog";
import { getNewNotifications, getNotifications } from "@/Data/Notification";
import LogoutIcon from "@mui/icons-material/Logout";
import { formatDateTimeUTC } from "@/components/HelperFunctions";
import { signOut } from "next-auth/react";
import { mutationAddNotificationView } from "@/Data/Announcement";
// import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import {
  getCurrentLocalTime,
  getFormattedDate,
  useSelectedColor,
} from "@/components/HelperFunctions";
import { useMutation } from "@apollo/client";
import AppBar from "@mui/material/AppBar";
import Image from "next/image";
import Badge from "@mui/material/Badge";
import {
  Popper,
  Fade,
  Paper,
  ClickAwayListener,
  MenuItem,
  MenuList,
  Select,
  ListItem,
  ListItemText,
  Collapse,
  ListItemIcon,
  ListItemButton,
  Slide,
} from "@mui/material";
import {
  Notifications,
  DarkMode,
  LightMode,
  StarBorder,
  Campaign,
  Group,
  Celebration,
  EventAvailable,
  FormatQuote,
  NotificationsNoneRounded,
  NotificationsActiveRounded,
  Circle,
} from "@mui/icons-material";

import Toolbar from "@mui/material/Toolbar";
import MenuIcon from "@mui/icons-material/Menu";
import IconButton from "@mui/material/IconButton";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ClearIcon from "@mui/icons-material/Clear";
import InformationController from "@/components/InformationController";
import useMediaQuery from "@mui/material/useMediaQuery";
import Link from "next/link";
import { useTheme } from "@mui/material/styles";
import Divider from "@mui/material/Divider";
import { useColor } from "@/components/ColorContext";
import Grow from "@mui/material/Grow";
import {
  getSortedAnnouncements,
  separateLatestData,
} from "@/components/HelperFunctions";
import { GET_BOOKINGS_OF_ROLE, GET_BOOKINGS_OF_TEA_BOY } from "@/Data/Booking";
import { useQuery } from "@apollo/client";
import withAuth from "@/components/auth/withAuth";
import { getInternalNews } from "@/Data/News";
import { green } from "@mui/material/colors";
import { useSession } from "next-auth/react";
// import Link from "next/link";

const ResponsiveBox = styled(Box)(
  ({ theme, backgroundColor, disableBorder, isCurrentAnnouncement }) => {
    const lightBackground = "#F6F5FD"; // Default background for light theme

    const background = isCurrentAnnouncement
      ? theme.palette.background.secondary || lightBackground
      : lightBackground;

    const borderColor = isCurrentAnnouncement
      ? `linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)`
      : "none";

    return {
      width: "100%",
      height: 220,
      background: backgroundColor || "white",
      backgroundColor: background,
      boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
      borderRadius: 10,
      position: "relative",

      "&::before": !disableBorder && {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0.6,
        width: "5px",
        height: "100%",
        background: borderColor,
        borderRadius: "20px 0 0 20px",
      },
    };
  }
);

const TicketScreen = () => {
  const router = useRouter();
  const [reloadCounter, setReloadCounter] = useState(0);
  // ... (your existing code)

  const handleSignOut = async () => {
    // Sign out the user
    await signOut({ redirect: false });

    // Redirect the user to the login page
    router.push("/login");
  };
  // Use useEffect to set up an interval that increments the counter every minute
  useEffect(() => {
    const intervalId = setInterval(() => {
      setReloadCounter((prevCounter) => prevCounter + 1);
    }, 1000); // 60 seconds

    // Clear the interval when the component is unmounted
    return () => clearInterval(intervalId);
  }, [reloadCounter]);

  useEffect(() => {
    // Reload the entire page when reloadCounter changes
    if (reloadCounter > 0 && reloadCounter % 60 === 0) {
      router.reload();
    }
  }, [reloadCounter, router]);

  const theme = useTheme();

  const handleNotificationToggle = () => {
    setNotificationOpen((prevOpen) => !prevOpen);
  };
  const handleNotificationClose = (event) => {
    if (
      notificationAnchorRef.current &&
      notificationAnchorRef.current.contains(event.target)
    ) {
      return;
    }

    setNotificationOpen(false);
  };
  // const handleCloseModal = () => {
  //   setOpenModal(false);
  // };
  // const [display, setDisplay] = useState("announcements");
  const smallScreen = useMediaQuery("(min-width:700px)");
  const xSmallScreen = useMediaQuery("(max-width:700px)");
  const isLargeTablet = useMediaQuery("(max-width:1079px)");

  const isStandardDesktop = useMediaQuery("(min-width:967px)");

  const isXXLargeScreen = useMediaQuery("(min-width:1921px)");

  const isXLargeScreen = useMediaQuery(
    "(min-width:1701px) and (max-width:1920px)"
  );

  const isExtraLargeScreen = useMediaQuery(
    "(min-width:1401px) and (max-width:1700px)"
  );
  const isLargeScreen = useMediaQuery(
    "(min-width:1201px) and (max-width:1400px)"
  );
  const isMediumScreen2 = useMediaQuery(
    "(min-width:1025px) and (max-width:1200px)"
  );
  const isMediumScreen = useMediaQuery(
    "(min-width:769px) and (max-width:1024px)"
  );
  const isSmallScreen = useMediaQuery(
    "(min-width:481px) and (max-width:768px)"
  );
  const isExtraSmallScreen = useMediaQuery("(max-width:480px)");

  // const currentAnnouncements = announcements.slice(0, 2);
  // const previousAnnouncements = announcements.slice(2);

  const { data: session } = useSession();

  const { color } = useColor();
  const selectedColor = useSelectedColor(color);
  const [selectedIcon, setSelectedIcon] = useState(<LightMode color="white" />);
  const [anchorEl, setAnchorEl] = useState(null);
  const [bookings, setBookings] = useState([
    // Your existing bookings array...
    {
      id: 1,
      title: "asdasd",
      start: "2024-01-02T10:30:00.000Z",
      end: "2024-01-02T11:00:00.000Z",
      details: "Starts at 03:30 PM and ends at 04:00 PM",
      parking: false,
      teaBoy: true,
      itTechnician: true,
      uid: "040000008200E00074C5B7101A82E008000000000812750B633DDA010000000000000000100000000296E9182F244F43B66A874A88DC6D0E",
      user_id: 253,
    },
  ]);
  const calculateTimeDifference = (startTime, endTime) => {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const timeDiffInMilliseconds = end - start;
    const timeDiffInMinutes = Math.floor(timeDiffInMilliseconds / (1000 * 60));

    return timeDiffInMinutes;
  };
  // const {
  //   loading: queryLoading,
  //   error: queryError,
  //   data: { announcements: announcementsData } = {},
  // } = useQuery(getAnnouncements);

  const {
    loading,
    error,
    data: queryData,
  } = useQuery(getNewNotifications, {
    variables: { user_id: session && session.user.id },
  });

  // console.log(announcementsData);
  // const [books, setBookings] = useState([]);

  // useEffect(() => {
  //   const fetchNews = async () => {
  //     try {
  //       let response = await getInternalNews();

  //       console.log("internal", separateLatestData(response.data));
  //       setNews(separateLatestData(response.data));
  //     } catch (error) {
  //       console.log(error);
  //     }
  //   };
  //   fetchNews();
  // }, []);
  const {
    bookingLoading,
    bookingError,
    data: bookingQueryData,
  } = useQuery(GET_BOOKINGS_OF_TEA_BOY);

  useEffect(() => {
    if (
      !bookingLoading &&
      !bookingError &&
      bookingQueryData &&
      bookingQueryData.bookingsOfTeaBoy
    ) {
      const bookingsOfTeaBoyData = bookingQueryData.bookingsOfTeaBoy;
      console.log("bookingsOfTeaBoyData", bookingsOfTeaBoyData);
      setBookings(bookingsOfTeaBoyData);
    }
  }, [bookingLoading, bookingError, bookingQueryData]);

  const { setGlobalColor } = useColor();

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // const handleColorPreferenceChange = (color, icon) => {
  //   // setMode(theme);
  //   setGlobalColor(color);
  //   setSelectedIcon(icon);
  //   handleClose();
  // };
  // const handleThemesChange = (theme, icon) => {
  //   localStorage.setItem("theme", theme);
  //   setMode(theme);
  //   setSelectedIcon(icon);
  //   handleClose();
  // };

  const [notificationOpen, setNotificationOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [notifications, setNotifications] = useState([]);
  const notificationAnchorRef = useRef(null);
  useEffect(() => {
    if (!loading && !error && queryData && queryData.newNotificationsForUser) {
      const notificationsData = queryData.newNotificationsForUser;
      console.log(notificationsData);
      setNotifications(notificationsData);
    }
  }, [loading, error, queryData]);
  const handleSetMoreItemlick = (event) => {
    setMoreItem(!moreItem);
  };
  const [addNotificationView] = useMutation(mutationAddNotificationView);

  const removeAnnouncementHandler = async (notificationId) => {
    console.log("remove", notificationId);

    try {
      console.log(session.user.id);
      const response = await addNotificationView({
        variables: {
          notificationId: notificationId,
          user_id: session && session.user.user_id,
        },
      });
      // Filter out the announcement with the specified ID
      if (response) {
        const updatedNotifications = notifications.filter(
          (notification) => notification.id !== notificationId
        );
        setNotifications(updatedNotifications);
      }
      setSnackbarMessage("Mark as viewed successfully");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error(error);
      setSnackbarMessage("Deletion failed");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  const currentDate = new Date();
  const upcomingBookings = bookings
    .filter((bookings) => new Date(bookings.start) >= currentDate)
    .sort((a, b) => new Date(a.start) - new Date(b.start));
  const previousBookings = bookings
    .filter((bookings) => new Date(bookings.start) < currentDate)
    .sort((a, b) => new Date(b.start) - new Date(a.start));
  const itemsPerRow = 3;

  const logoSource = "/HassanaLogoD.png";
  // const itemsPerRow = Math.min(3, responsiveBoxData.length);
  // console.log(news[1])
  return (
    <>
      <AppBar position="absolute">
        <Toolbar
          sx={{
            position: "fixed",
            width: "100%",
            backgroundColor: "#063F53",
            zIndex: 1,
            borderTop: `4px solid ${theme.palette.text.purple}`,
            borderBottom: `4px solid ${theme.palette.text.purple}`,
            [theme.breakpoints.down("xs")]: {
              flexDirection: "column",
            },
          }}
        >
          <Box
            component="h1"
            variant="h6"
            color="inherit"
            noWrap
            sx={{
              flexGrow: 1,
              display: "flex",
              alignItems: "center",
              gap: "30px",
            }}
          >
            <Link href="/">
              <Image
                src={logoSource}
                alt="Logo"
                loading="lazy"
                width={xSmallScreen ? 80 : 180}
                height={42}
              />
            </Link>{" "}
            <Divider
              orientation="vertical"
              sx={{ backgroundColor: "#307188" }}
              flexItem
            />
            <Typography
              sx={{
                fontSize: "28px",
                color: "#FFF",
                fontWeight: 300,
                display: smallScreen ? "block" : "none",
              }}
            >
              {/* Office Boy */}
              {session?.user?.role}
            </Typography>
          </Box>

          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: "10px",
            }}
          >
            <IconButton
              color="inherit"
              ref={notificationAnchorRef}
              onClick={handleNotificationToggle}
            >
              <Badge
                badgeContent={notifications ? notifications.length : 0}
                color="secondary"
              >
                <Notifications />
              </Badge>
            </IconButton>
            <Popper
              open={notificationOpen}
              anchorEl={notificationAnchorRef.current}
              role={undefined}
              transition
              sx={{
                maxHeight: notifications.length > 4 ? "525px" : "auto",
                overflowY: notifications.length > 4 ? "auto" : "visible",
              }}
              disablePortal
              popperOptions={{
                modifiers: [
                  {
                    name: "offset",
                    options: {
                      offset: [0, 10], // Adjust the second value for space from the top
                    },
                  },
                  {
                    name: "preventOverflow",
                    options: {
                      padding: 10, // Adjust the padding as needed
                    },
                  },
                ],
                // placement: "top-end", // Position at the top-right
              }}
            >
              {({ TransitionProps, placement }) => (
                <Grow {...TransitionProps} timeout={350}>
                  <Paper>
                    <ClickAwayListener onClickAway={handleNotificationClose}>
                      <Typography
                        // Optional: Add borderRadius for rounded corners
                        boxShadow={10} // You can adjust the shadow level (0 to 24)
                        //  borderRadius={2}
                        variant="body2"
                        sx={{
                          p: 2,
                          mt: 2,
                          display: "flex",
                          flexDirection: "column",
                          background:
                            selectedColor == theme.palette.background.primary
                              ? theme.palette.background.secondary
                              : selectedColor,
                        }}
                      >
                        {loading ? (
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "center",
                            }}
                          >
                            <CircularProgress color="secondary" />
                          </Box>
                        ) : (
                          <>
                            {notifications.length > 0 ? (
                              notifications.map((notification, index) => {
                                // console.log(notification);
                                return (
                                  <div
                                    key={index}
                                    style={{
                                      display: "flex",
                                      gap: "10px",
                                      marginBottom: "29px",
                                      alignItems: "center",
                                      maxWidth: "400px",
                                    }}
                                  >
                                    <div style={{ flex: 1 }}>
                                      <Image
                                        width={38}
                                        height={38}
                                        alt="Icon"
                                        src="/icons/ellipse.png"
                                      />
                                    </div>
                                    <div
                                      display={"flex"}
                                      style={{
                                        flex: 5,
                                        flexDirection: "column",
                                      }}
                                    >
                                      <Typography
                                        variant="subtitle1"
                                        sx={{
                                          fontSize: "14px",
                                          width: "100%", // Ensure full width
                                          lineHeight: "20px",
                                        }}
                                      >
                                        {notification.notification}
                                      </Typography>
                                      {/* Other components */}
                                      <Typography
                                        fontSize={"12px"}
                                        color={"#A7A7A7"}
                                        marginTop={"7px"}
                                      >
                                        {formatDateTimeUTC(
                                          notification.createdAt
                                        )}
                                      </Typography>
                                      <Typography
                                        variant="body1"
                                        color="gray"
                                        sx={{
                                          borderBottom: 1,
                                          marginTop: "11.89px",
                                        }}
                                      ></Typography>
                                    </div>
                                    <IconButton
                                      sx={{
                                        flex: 0.5,
                                        color: "#A7A7A7",
                                        padding: "0",
                                      }}
                                      onClick={() =>
                                        removeAnnouncementHandler(
                                          notification.id
                                        )
                                      }
                                    >
                                      <ClearIcon />
                                    </IconButton>
                                  </div>
                                );
                              })
                            ) : (
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "center",
                                }}
                              >
                                <Typography
                                  variant="h5"
                                  sx={{ marginY: "10px" }}
                                >
                                  No data found
                                </Typography>
                              </Box>
                            )}
                          </>
                        )}

                        <Link href="/notifications" passHref>
                          <Typography
                            component="a"
                            variant="body1"
                            sx={{
                              marginTop: 1,
                              display: "flex",
                              fontSize: "15px",
                              // textAlign:"center",
                              // marginX: "auto",
                              fontWeight: 500,
                              textDecoration: "none", // Remove underline from the link
                              color: "inherit", // Use the default text color for links
                              "&:hover": {
                                color: "primary.main", // Change color on hover
                              },
                            }}
                          >
                            View All Notifications
                            <ArrowForwardIcon sx={{ marginLeft: 1 }} />
                          </Typography>
                        </Link>
                      </Typography>
                    </ClickAwayListener>
                  </Paper>
                </Grow>
              )}
            </Popper>
            <svg
              onClick={handleSignOut}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M8.90002 7.56023C9.21002 3.96023 11.06 2.49023 15.11 2.49023H15.24C19.71 2.49023 21.5 4.28023 21.5 8.75023V15.2702C21.5 19.7402 19.71 21.5302 15.24 21.5302H15.11C11.09 21.5302 9.24002 20.0802 8.91002 16.5402"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2 12H14.88"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.65 8.65039L16 12.0004L12.65 15.3504"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <Typography onClick={handleSignOut}>Sign Out</Typography>
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          backgroundColor: "#FCFAFF",
        }}
      >
        <Box
          component="main"
          sx={{
            background:
              selectedColor == theme.palette.background.primary
                ? theme.palette.background.secondary
                : selectedColor,
            paddingLeft: isLargeTablet ? "50px" : "",
            paddingRight: isLargeTablet ? "50px" : "",
            width: isXXLargeScreen
              ? "70vw"
              : isXLargeScreen
              ? "90vw"
              : isExtraLargeScreen
              ? "80vw"
              : isLargeScreen
              ? "89vw"
              : isMediumScreen2
              ? "95vw"
              : isMediumScreen
              ? "90vw"
              : isSmallScreen
              ? "90vw"
              : isExtraSmallScreen
              ? "90vw"
              : "60vw",
            margin: "auto",
          }}
        >
          <Box>
            <Box
              style={{
                marginTop: "100px",
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <Box>
                <Typography
                  variant="h5"
                  sx={{
                    fontSize: {
                      lg: "16px",
                      md: "18px",
                      sm: "15px",
                      xs: "12px",
                    },
                  }}
                >
                  Latest Tickets from Hassana
                  <Divider
                    sx={{
                      width: "70%",
                      border: "1px solid #00BC82",
                    }}
                  />
                </Typography>
                <Typography
                  variant="h1"
                  style={{
                    fontWeight: "700",
                    marginTop: "20px",
                  }}
                  sx={{
                    fontSize: {
                      lg: "27px",
                      md: "24px",
                      sm: "20px",
                      xs: "15px",
                    },
                  }}
                >
                  New Tickets
                </Typography>
              </Box>
              <Typography
                variant="h5"
                sx={{
                  fontSize: { lg: "29px", md: "25px", sm: "20px", xs: "15px" },
                }}
                style={{
                  fontWeight: "700",
                }}
              >
                {getCurrentLocalTime()}
                <br />
                {getFormattedDate()}
              </Typography>
            </Box>

            <Divider
              sx={{
                marginTop: "20px",
                width: "30%",
                border: "1px solid #A665E1",
              }}
            />
          </Box>
          <Grid
            container
            spacing={3}
            sx={{ paddingTop: "3%", justifyContent: "center" }}
          >
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center" }}>
                <CircularProgress color="secondary" />
              </Box>
            ) : (
              <>
                {upcomingBookings.length > 0 ? (
                  upcomingBookings.map((box, index) => {
                    const itemsInLastRow =
                      upcomingBookings.length % itemsPerRow.md ||
                      itemsPerRow.md;
                    const totalRows = Math.ceil(
                      upcomingBookings.length / itemsPerRow.md
                    );
                    const isLastRow =
                      Math.floor(index / itemsPerRow.md) === totalRows - 1;
                    const timeDifference = calculateTimeDifference(
                      box.start,
                      box.end
                    );
                    return (
                      <Grid
                        item
                        key={index}
                        xs={12} // Full width on small screens
                        sm={6} // 2 boxes in a row on medium screens
                        md={isLastRow ? itemsInLastRow : 4} // Dynamically calculate itemsPerRow
                        sx={{ textAlign: isLastRow ? "center" : "left" }}
                      >
                        <ResponsiveBox sx={{ backgroundColor: "#fff" }}>
                          <AnnouncementCard
                            title={box.location}
                            heading={box.title}
                            details={box.details}
                            startTime={box.start}
                            endTime={box.end}
                            button={`${timeDifference} m`}
                          />
                        </ResponsiveBox>
                      </Grid>
                    );
                  })
                ) : (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Typography variant="h5" sx={{ marginY: "10px" }}>
                      No upcoming bookings found
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Grid>

          <Box
            display="flex"
            alignItems="center"
            marginBottom="24px"
            marginTop="24px"
          >
            <Box borderBottom="2px solid #E2E0F1" width="100%"></Box>
            <Typography
              variant="body2"
              align="center"
              sx={{
                mx: 0,
                color: "#949494",
                fontSize: "12px",
                whiteSpace: "nowrap",
              }}
            >
              Previous Tickets
            </Typography>
            <Box borderTop="2px solid #E2E0F1" width="100%"></Box>
          </Box>
          <Grid container spacing={3} sx={{ justifyContent: "center" }}>
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center" }}>
                <CircularProgress color="secondary" />
              </Box>
            ) : (
              <>
                {previousBookings.length > 0 ? (
                  previousBookings.map((box, index) => {
                    const itemsInLastRow =
                      previousBookings.length % itemsPerRow.md ||
                      itemsPerRow.md;
                    const totalRows = Math.ceil(
                      previousBookings.length / itemsPerRow.md
                    );
                    const isLastRow =
                      Math.floor(index / itemsPerRow.md) === totalRows - 1;
                    const timeDifference = calculateTimeDifference(
                      box.start,
                      box.end
                    );
                    return (
                      <Grid
                        item
                        key={index}
                        xs={12} // Full width on small screens
                        sm={6} // 2 boxes in a row on medium screens
                        md={isLastRow ? itemsInLastRow : 4} // Dynamically calculate itemsPerRow
                        sx={{ textAlign: isLastRow ? "center" : "left" }}
                      >
                        <ResponsiveBox>
                          <AnnouncementCard
                            title={box.location}
                            heading={box.title}
                            details={box.details}
                            startTime={box.start}
                            endTime={box.end}
                            button={`${timeDifference} m`}
                          />
                        </ResponsiveBox>
                      </Grid>
                    );
                  })
                ) : (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Typography variant="h5" sx={{ marginY: "10px" }}>
                      No previous bookings found
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default withAuth(TicketScreen);
