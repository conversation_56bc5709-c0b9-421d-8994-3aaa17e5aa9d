import { gql } from "@apollo/client";

export const getLeaves = gql`
  query{
    leaves {
        id
        username
        user_id
        date
        remarks
        numberOfDays
        typeOfLeave
        createdAt
    }
}
`;
// query{
//   getUserLeaves(id:1){
//     medical
//     casual
//   }
// }
export const getUserLeave = gql`
  query GetUserLeaves($Id: ID!){
    getUserLeaves(id: $Id) {
      medical
      casual
    }
}
`;

export const mutationCreateLeave = gql`
mutation CreateLeave(
    $user_id: Int!
    $username: String!
    $date: DateTime!
    $numberOfDays: Int!
    $typeOfLeave: String!
    $remarks: String!
) {
    createLeave(createLeaveInput: {
        user_id: $user_id
        username: $username
        date: $date
        numberOfDays: $numberOfDays
        typeOfLeave: $typeOfLeave
        remarks: $remarks
  }) {
    id
    username
    user_id
    remarks
    date
    numberOfDays
    typeOfLeave
    createdAt
  }
}
`;

export const mutationRemoveLeave = gql`
  mutation RemoveLeave($id: ID!) {
    removeLeave(id: $id) {
        username,
        remarks,
        user_id,
        date,
        numberOfDays,
        typeOfLeave
    }
  }
`;


export const mutationUpdateLeave = gql`
  mutation UpdateLeave(
    $id: Int!
    $user_id: Int!
    $username: String!
    $date: DateTime!
    $numberOfDays: Int!
    $typeOfLeave: String!
    $remarks: String!
  ) {
    updateLeave(updateLeaveInput: {
      id: $id
      user_id: $user_id
      username: $username
      date: $date
      numberOfDays: $numberOfDays
      typeOfLeave: $typeOfLeave
      remarks: $remarks
    }) {
      id
      username
      user_id
      date
      remarks
      numberOfDays
      typeOfLeave
      createdAt
    }
}
`;