import * as React from "react";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Image from "next/image";
import { styled } from "@mui/system";
import Link from "next/link";
import { useTheme, Divider, Box } from "@mui/material";
import { useRouter } from "next/router";
import { signOut, useSession } from "next-auth/react";
import { useMode } from "./ModeContext";

// Custom ListItemText
const CustomListItemText = styled(ListItemText)({
  textEdge: "cap",
  fontFamily: "Urbanist",
  fontSize: "1rem",
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "normal",
});

// Base menu list
const listItems = [
  {
    icon: "home",
    alt: "Home Icon",
    primary: "Home",
    href: "/",
  },
  {
    icon: "notification",
    alt: "Notification Icon",
    primary: "Notifications",
    href: "/notifications",
  },
  // {
  //    icon: "employees",
  //    alt: "My Profile Icon",
  //    primary: "My Profile",
  //   href: "/myprofile",
  //  },
   
  {
    icon: "announcement",
    alt: "Announcement Icon",
    primary: "News & Announcements",
    href: "/news",
  },
  {
    icon: "employees",
    alt: "My Profile Icon",
    primary: "Address Book",
    href: "", // This will be set dynamically based on user role
  },
  {
    icon: "content",
    alt: "Hassana Library Icon",
    primary: "Hassana Library",
    href: "/hassanalibrary",
  },
  {
    icon: "calendar",
    alt: "Calendar Icon",
    primary: "My Calendar",
    href: "/allocator",
  },
  {
    icon: "settings",
    alt: "Settings Icon",
    primary: "Settings",
    href: "/",
  },
  {
    icon: "login",
    alt: "SignOut Icon",
    primary: "Sign Out",
  },
];

export const MainListItems = () => {
  const router = useRouter();
  const theme = useTheme();
  const { mode } = useMode();
  const { data: session } = useSession();

  const userRole = session?.user?.role || "user"; // default to 'user' if role not present

  // Update the href of Address Book based on role
  const updatedListItems = listItems.map((item) => {
    if (item.primary === "Address Book") {
      return {
        ...item,
        href: "/AddressBook",
      };
    }
    return item;
  });

  return (
    <React.Fragment>
      {updatedListItems.slice(0, 6).map((item) => {
        const isActive = router.pathname === item.href;
        const IconSrc = isActive
          ? `/NavIcons/selected/${item.icon}.svg`
          : mode !== "light"
          ? `/NavIcons/dark/${item.icon}.svg`
          : `/NavIcons/light/${item.icon}.svg`;

        return (
          <Link key={item.primary} href={item.href}>
            <ListItemButton
              sx={{
                backgroundColor: isActive
                  ? mode !== "light"
                    ? "#383838"
                    : "#FFF"
                  : "none",
              }}
            >
              <ListItemIcon>
                <Image src={IconSrc} alt={item.alt} width={24} height={24} />
              </ListItemIcon>
              <CustomListItemText
                primary={item.primary}
                sx={{ color: theme.palette.text.primary }}
              />
            </ListItemButton>
          </Link>
        );
      })}
    </React.Fragment>
  );
};

export const SecondaryListItems = () => {
  const router = useRouter();
  const theme = useTheme();
  const { mode } = useMode();
  const { data: session } = useSession();

  const userRole = session?.user?.role || "user";

  const updatedListItems = listItems.map((item) => {
    if (item.primary === "Address Book") {
      return {
        ...item,
        href: "./AddressBook",
      };
    }
    return item;
  });

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push("/login");
  };

  return (
    <Box>
      {updatedListItems.slice(6).map((item) => {
        const IconSrc =
          mode !== "light"
            ? `/NavIcons/dark/${item.icon}.svg`
            : `/NavIcons/light/${item.icon}.svg`;

        if (item.primary === "Sign Out") {
          return (
            <ListItemButton key={item.primary} onClick={handleSignOut}>
              <ListItemIcon>
                <Image src={IconSrc} alt={item.alt} width={24} height={24} />
              </ListItemIcon>
              <CustomListItemText
                primary={item.primary}
                sx={{ color: theme.palette.text.primary }}
              />
            </ListItemButton>
          );
        }

        return (
          <Link key={item.primary} href={item.href}>
            <ListItemButton>
              <ListItemIcon>
                <Image src={IconSrc} alt={item.alt} width={24} height={24} />
              </ListItemIcon>
              <CustomListItemText
                primary={item.primary}
                sx={{ color: theme.palette.text.primary }}
              />
            </ListItemButton>
          </Link>
        );
      })}
    </Box>
  );
};
