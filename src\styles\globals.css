@import url(https://fonts.googleapis.com/css2?family=Urbanist:wght@400;600;700&display=swap);
@import url(https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;600;700&display=swap);
/* :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
  } */

* {
  font-family: "Urbanist", sans-serif !important;
  font-weight: 600;
  --fc-today-bg-color: rgba(208, 208, 208, 0.3) !important;
  cursor: default !important;
}

/* But allow text cursor for editable areas */
input:not([readonly]):not([disabled]),
textarea:not([readonly]):not([disabled]),
[contenteditable="true"] {
  cursor: text !important;
}

/* Optional: cursor for clickable elements */
button,
a,
[role="button"],
.MuiIconButton-root {
  cursor: pointer;
}



.fc .fc-timegrid-slot-minor {
  /* border-top-style: groove;
  border-color: red; */
  border-top: 1px solid #A4A4A4;
}

.fc-event-main {
  display: flex;
}

/* .fc-event-main{
  background-color: #000;
} */

/* .css-9yjdhh-MuiDialogContent-root {
  background: #a665e1;
}

.css-1r39cda {
  background: #a665e1;
}
.css-1g7nc1s-MuiPickersLayout-root {
  background: #a665e1;
} */

.css-192l38i {
  background: #a665e1;
}

.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child) {
  text-transform: capitalize;
}

.fc .fc-button-group>.fc-button {
  text-transform: capitalize;

  color: #a665e1 !important;

}

.css-1anx036 {
  font-family: "Urbanist", sans-serif !important;
  font-weight: 700 !important;
}

.css-2ulfj5-MuiTypography-root {
  font-family: "Urbanist", sans-serif !important;
  font-weight: 700 !important;
}

body {
  min-width: 100vw;
  width: 100vw !important;
  max-width: 100vw;
}

.fc .fc-button {
  font-size: 1rem;
  background: none;
  border: none;
  color: #000 !important;
}

.fc .fc-toolbar-title {
  font-size: 1rem;
  color: #a665e1 !important;
}

.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child) {
  background: none;
  border: none;
  color: #a665e1 !important;
}

.fc .fc-button-primary:not(:disabled).fc-button-active:focus {
  box-shadow: none;
  color: #a665e1 !important;
}

.fc .fc-button-primary:not(:disabled):active:focus {
  box-shadow: none;
}

.fc .fc-button-primary:hover {
  background: none;
}

.fc .fc-button .fc-icon {
  color: #a665e1 !important;
}

.fc .fc-button-primary:focus {
  box-shadow: none;
}

.fc .fc-scrollgrid-liquid {
  border: none;
}

/* .fc .fc-col-header-cell-cushion {
    color: #d3d3d3;
  } */
.fc-theme-standard td {
  border: none;
}

.fc .fc-toolbar-chunk div {
  display: flex !important;
  align-items: center;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: none;
}

primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background: none !important;
}

.fc-direction-ltr .fc-daygrid-event.fc-event-end {
  background-color: #F7EFFF;
  /* background-color: #169b71; */
  color: #000;
}

.fc .fc-button-primary:not(:disabled).fc-button-active {
  background-color: transparent;
  border-color: none !important;
}

.fc .fc-daygrid-day.fc-day-today {
  background-color: rgb(224 216 216 / 15%);
}

/* @media (prefers-color-scheme: dark) {
    :root {
      --foreground-rgb: 255, 255, 255;
      --background-start-rgb: #252525;
      --background-end-rgb: 0, 0, 0;
    }
  }

  body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(
        to bottom,
        transparent,
        rgb(var(--background-end-rgb))
      )
      rgb(var(--background-start-rgb));
  } */
::-webkit-scrollbar {
  width: 6px;
  height: 2px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #a665e1;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a665e1;
}

.css-1tsk39f {
  overflow: hidden;
}