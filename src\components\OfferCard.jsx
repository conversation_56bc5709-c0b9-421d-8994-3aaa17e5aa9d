import React from 'react';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useRouter } from 'next/router';
import { breakpoints } from '@/helper/mediaQueries'; // assuming same path as your Celebration component

const OfferCard = () => {
  const router = useRouter();
  const theme = useTheme();
  
  // Responsive breakpoints
  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;

  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);
  const isXXLargeScreen = useMediaQuery(xxLargeScreen);

  const handleClick = () => {
    router.push('../../HassanaOffers');
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        // width: '100%',
        padding: isSmallScreen ? 2 : isMediumScreen ? 3 : 4,
        // maxWidth: isLargeScreen
        //   ? 480
        //   : isMediumScreen
        //   ? 400
        //   : isSmallScreen
        //   ? '100%'
        //   : 500,
        minHeight:"210px",
        // backgroundColor: theme.palette.background.paper,
        cursor: 'pointer',
        transition: 'transform 0.2s ease',
        '&:hover': {
          transform: 'scale(1.02)',
         
        },
      }}
    >
      <Typography
        sx={{
          fontSize: isSmallScreen ? '17px' : isMediumScreen ? '20px' : '22px',
          color: theme.palette.text.primary,
          my: 2,
          fontWeight: 500,
        }}
      >
        <Box
          component="span"
          sx={{
            borderBottom: `2px solid ${theme.palette.success.main}`,
            display: 'inline-block',
            pb: '2px',
          }}
        >
         <span>  Enhance Employee{' '}</span>
          <span style={{ color: theme.palette.text.primary, fontWeight: 'bold' }}>Financial</span>
        </Box>
        <span style={{ color: theme.palette.text.primary, fontWeight: 'bold' }}> Wellness</span>
      </Typography>

      <Typography
        variant="body2"
        sx={{
          color: theme.palette.text.secondary,
          width: '100%',
          fontSize: isSmallScreen ? '14px' : isMediumScreen ? '15px' : '16px',
        }}
      >
        Empower your team with exclusive savings, personalized offers, and valuable perks through{' '}
        <Box component="span" sx={{ color: theme.palette.text.primary, fontWeight: 'bold' }}>
          WalaOffer
        </Box>
      </Typography>
    </Box>
  );
};

export default OfferCard;
