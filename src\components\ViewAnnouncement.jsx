// File: ViewAnnouncement.js
import React from "react";
import { Box } from "@mui/material";
import { useTheme } from "@mui/material";

const ViewAnnouncement = ({ ResponsiveBox, selectedAnnouncement }) => {
  const theme = useTheme();
  const defaultBackgroundImage = "./images/Rectangle84.png";

  return (
    <ResponsiveBox
      sx={{ width: "100%",height:"auto",marginTop:"4rem"}}
      disableBorder
      isCurrentAnnouncement={true}
    >
      <Box>
        <img
          src={selectedAnnouncement.image || defaultBackgroundImage}
          style={{ width: "100%" }}
        />
      </Box>
    </ResponsiveBox>
  );
};

export default ViewAnnouncement;
