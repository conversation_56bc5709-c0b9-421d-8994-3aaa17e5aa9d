import React, { useState, useEffect } from "react";
import {
  Drawer,
  useTheme,
  IconButton,
  Box,
  Typography,
  Slide,
  Grid,
} from "@mui/material";
import Slider from "react-slick";
import useMediaQuery from "@mui/material/useMediaQuery";
import { SvgIcon } from "@mui/material";
import News from "./News";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

const baseSettings = {
  infinite: false,
  speed: 1200,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 2000,
  arrows: false,
  infinite: true,
};
const settings = {
  ...baseSettings,
  dots: false,
};

const verticalSettings = {
  ...baseSettings,
  dots: false,
  vertical: true,
};

const verticalSlides = [{ title: "Vision" }, { title: "Mission" }];

const horizontalSlides = [
  {
    content:
      "To be a global benchmark in investing for national social security",
  },
  {
    content:
      "To invest for the long term across global asset classes, using our thoughtful approach, robust process, and world-class talent",
  },
];

const secondslides = [
  {
    content:
      "We empower our people, We encourage our people to take charge of tasks. We empower them to keep learning and upskilling.",
  },
  {
    content:
      "We consider broader and think deeper to deliver results, while managing risk on par with global standards.",
  },
];

const SliderDiv = ({ children }) => (
  <div
    style={{
      opacity: 1,
      width: "20.4375rem",
      height: "8.6875rem",
      borderRadius: "0.625rem",
      margin: "auto",
      textAlign: "center",
      background:
        "linear-gradient(180deg, rgba(43, 78, 96, 0.50) 0%, rgba(55, 95, 116, 0.50) 100%)",
      display: "flex",
      alignItems: "center",
    }}
  >
    <h3 style={{ fontSize: "17px", color: "white" }}>{children}</h3>
  </div>
);

const SideBar = (props) => {
  const theme = useTheme();
  const { children } = props;

  const isExtraSmallScreen = useMediaQuery(
    "(min-width:300px) and  ((max-width:600px)"
  );
  const isSmallScreen = useMediaQuery(
    "(min-width:600px) and (max-width:1023px)"
  );
  const isMediumScreen = useMediaQuery(
    "(min-width:1025px) and (max-width:1300px)"
  );
  const isLargeScreen = useMediaQuery(
    "(min-width:1301px) and (max-width:1700px)"
  );
  const isBetween1023And1024 = useMediaQuery('(min-width: 1023px) and (max-width: 1024px)');
  const isXLargeScreen = useMediaQuery(
    "(min-width:1701px) and (max-width:1920px)"
  );

  const isxxLargeScreen = useMediaQuery("min-width:1921px)");
  const [isSidebarOpen, setSidebarOpen] = useState(
    !isSmallScreen && !isExtraSmallScreen
  );

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };
  useEffect(() => {
    if (!(isSmallScreen || isMediumScreen || isExtraSmallScreen)) {
      setSidebarOpen(true);
    } else {
      setSidebarOpen(false);
    }
  }, [isSmallScreen, isExtraSmallScreen]);

  const ArrowForwardPurpleIcon = (props) => (
    <SvgIcon {...props} fontSize="large">
      <circle cx="12" cy="12" r="10" fill="#F0EFFB" />
      <path d="M12 8l4 4-4 4" stroke="purple" strokeWidth="2" fill="none" />
    </SvgIcon>
  );

  const ArrowBackPurpleIcon = (props) => (
    <SvgIcon {...props} fontSize="large">
      <circle cx="12" cy="12" r="10" fill="#F0EFFB" />
      <path d="M12 16l-4-4 4-4" stroke="purple" strokeWidth="2" fill="none" />
    </SvgIcon>
  );

  return (
    <>
      {(isSmallScreen || isExtraSmallScreen) && (
        <IconButton
          onClick={toggleSidebar}
          edge="start"
          color="inherit"
          aria-label="menu"
          sx={{
            // position: "fixed",
            // left: isSidebarOpen
            //   ? isExtraSmallScreen
            //     ? "82vw"
            //     : isSmallScreen
            //     ? "50%"
            //     : "18rem"
            //   : "0",
            // top: "50%",
            // transform: "translateY(-50%)",
            // zIndex:"1000" 
            // position: isSmallScreen ? "absolute" : "absolute",
            top: "50vh",
            left: isSidebarOpen ? (isExtraSmallScreen ? "350px" : "22rem") : "0",

            cursor: "pointer",
            zIndex: isSidebarOpen ? 9998 : "1",
          }}
        >
          {isSidebarOpen ? <ArrowBackPurpleIcon /> : <ArrowForwardPurpleIcon />}
        </IconButton>
      )}

      <Drawer
        anchor="left"
        open={isSidebarOpen}
        onClose={toggleSidebar}
        variant={
          isSmallScreen || isExtraSmallScreen
            ? "temporary"
            : "permanent"
        }
        sx={{
          height:"100%",
          width: isSmallScreen
            ? "60%"
            : isLargeScreen
            ? "19vw"
            : isXLargeScreen
            ? "20vw"
            : isxxLargeScreen
            ? "20vw"
            : "20vw",
          flexShrink: 0,
        }}
      >
        <Grid container>
          <Grid item xs={12} sm={12} md={4} lg={4}>
            <div
              style={{
                minWidth:isExtraSmallScreen?"300px":"",
                width: isExtraSmallScreen
                  ? "22rem"
                  : isMediumScreen
                  ? "22rem"
                  : isLargeScreen
                  ? "26vw"
                  :isBetween1023And1024
                  ?"35vw"
                  : isXLargeScreen
                  ? "22vw"
                  : isSmallScreen
                  ? "22rem"
                  : "24vw",
                height: "100vh",
                overflow: "hidden",

                backgroundImage: `url(/mainpage.png)`,
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                objectFit: "cover",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: "8rem",
                    gap: "8px",
                  }}
                >
                  <div
                    style={{
                      width: isXLargeScreen ? "9vw" : "7vw",
                      height: "4px",
                      background: "#A665E1",
                      // margin: "-20px 0px 56px",
                      borderRadius: "0.625rem",
                    }}
                  ></div>

                  <h1
                    style={{
                      color: "white",
                      fontSize: isSmallScreen
                      ? "25px"
                      : isLargeScreen
                      ? "20px"
                      : "38px",
                      marginLeft: "1rem",
                    }}
                  >
                    Our
                  </h1>
                  <Slider
                    style={{ display: "block", width: "8rem" }}
                    {...verticalSettings}
                  >
                    {verticalSlides.map((slide, index) => (
                      <div key={index}>
                        <h1
                          style={{
                            color: "#00BC82",
                            fontSize: isSmallScreen
                            ? "25px"
                            : isLargeScreen
                            ? "20px"
                            : "38px",
                            fontWeight: "700",
                          }}
                        >
                          {slide.title}
                        </h1>
                      </div>
                    ))}
                  </Slider>
                </div>
              </div>
              <Slider style={{ marginTop: "40px" }} {...settings}>
                {horizontalSlides.map((slide, index) => (
                  <SliderDiv key={index}>{slide.content}</SliderDiv>
                ))}
              </Slider>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "3px",
                  marginTop: "20px",
                }}
              >
                <div
                  style={{
                    width: "37px",
                    height: "7px",
                    background: "#A665E1",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
                <div
                  style={{
                    width: "7px",
                    height: "7px",
                    background: "#62B6F3",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "right",
                 gap:"8px",
                  marginTop: "30px",
                }}
              >
                <Typography
                  sx={{
                    color: "#FFF",
                    fontWeight:"600",
                    marginLeft: "10px",
                    fontSize: isSmallScreen
                      ? "25px"
                      : isLargeScreen
                      ? "20px"
                      : "38px",
                  }}
                >
                  Our
                </Typography>
                <Typography
                  sx={{
                    color: "#A665E1",
                    fontWeight:"700",
                    fontSize: isSmallScreen
                      ? "25px"
                      : isLargeScreen
                      ? "20px"
                      : "38px",
                  }}
                >
                  Values
                </Typography>
                <div
                  style={{
                    width: "136px",
                    height: "4px",
                    background: "#00BC82",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
              </div>
              <Slider style={{ marginTop: "30px" }} {...settings}>
                {secondslides.map((slide, index) => (
                  <SliderDiv key={index}>
                    <div dangerouslySetInnerHTML={{ __html: slide.content }} />
                  </SliderDiv>
                ))}
              </Slider>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "3px",
                  marginTop: "20px",
                }}
              >
                <div
                  style={{
                    width: "37px",
                    height: "7px",
                    background: "#A665E1",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
                <div
                  style={{
                    width: "7px",
                    height: "7px",
                    background: "#62B6F3",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
                <div
                  style={{
                    width: "7px",
                    height: "7px",
                    background: "#62B6F3",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
                <div
                  style={{
                    width: "7px",
                    height: "7px",
                    background: "#62B6F3",
                    // margin: "-20px 0px 56px",
                    borderRadius: "0.625rem",
                  }}
                ></div>
              </div>
              <div style={{ width: "90%", marginLeft: "40px", bottom: "30px" }}>
                <Typography
                  sx={{
                    color: "#FFF",
                    fontSize: "12px",
                    fontFamily: "Helvetica",
                    marginTop: "80px",
                  }}
                >
                  Click here to view our{" "}
                  <span
                    style={{
                      textDecoration: "underline",
                      fontFamily: "Helvetica",
                    }}
                  >
                    Privacy policy, terms and conditions
                  </span>
                </Typography>
                <Typography
                  sx={{
                    color: "#FFF",
                    fontSize: "12px",
                    textAlign: "left",
                    fontFamily: "Helvetica",
                    margin: "20px 0px 50px 0px",
                  }}
                >
                  © Copyright 2023 Hassana
                </Typography>
              </div>
            </div>
          </Grid>
        </Grid>
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          marginLeft: isSmallScreen && isSidebarOpen ? "18rem" : 0,
          transition: "margin-left 0.3s ease-in-out",
        }}
      ></Box>
    </>
  );
};

export default SideBar;
