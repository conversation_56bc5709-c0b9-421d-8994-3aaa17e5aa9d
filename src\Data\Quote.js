import { gql } from '@apollo/client';

export const getTodaysQuote = gql`
  query {
    todaysQuote {
      id
      quote
      author
      status
    }
  }
`;

export const getQuotes = gql`
  query {
    findAllQuote {
      id
      quote
      author
      status
      visibilityStart
      visibilityEnd
      createdAt
      updatedAt
    }
  }
`;

export const mutationCreateQuote = gql`
  mutation createQuote($createQuoteInput: CreateQuoteInput!) {
  createQuote(createQuoteInput: $createQuoteInput) {
    id
    quote
    author
    status
    visibilityStart
    visibilityEnd
    createdAt
    updatedAt
  }
}
`;

export const mutationUpdateQuote = gql`
  mutation updateQuote($id: ID!, $updateQuoteInput: UpdateQuoteInput!) {
    updateQuote(id: $id, updateQuoteInput: $updateQuoteInput) {
      id
      quote
      author
      status
      visibilityStart
      visibilityEnd
      createdAt
      updatedAt
    }
  }
`;

export const mutationRemoveQuote = gql`
  mutation RemoveQuote($id: ID!) {
    removeQuote(id: $id) {
      id
      quote
      author
      status
      visibilityStart
      visibilityEnd
      createdAt
      updatedAt
    }
  }
`;