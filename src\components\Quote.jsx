import {
  Avatar,
  AvatarGroup,
  Box,
  Grid,
  IconButton,
  Modal,
  Typography,
  useTheme,
} from "@mui/material";
import MailOutline from "@mui/icons-material/MailOutline";
import CircularWithValueLabel from "./CircularWithValueLabel";
import Image from "next/image";
import BasicModal from "./Modal";
import { getTodaysQuote } from "@/Data/Quote";
import { useQuery } from "@apollo/client";

const Quote = () => {
  const theme = useTheme();
  const { loading, error, data } = useQuery(getTodaysQuote);
  return (
    <>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: "10px",
          boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
          padding: "20px",
          minHeight: "210px",
          cursor: "default"
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          fontSize="1.3rem"
          fontWeight="700"
        >
          Quote of the week
        </Typography>
        {data && data.todaysQuote ? (
          <QuoteBox data={data} />
        ) : (
          <Box
            sx={{
              minHeight: "127px",
              paddingTop: "40px",
              textAlign: "center",
              fontWeight: 700,
            }}
          >
            {" "}
            No Quote For this week{" "}
          </Box>
        )}
      </Box>
    </>
  );
};

export default Quote;

const QuoteBox = ({ data }) => {
  const theme = useTheme();
  return (
    <Box sx={{ minHeight: "125px", position: "relative", cursor: "default" }}>
      <Image
        src="/quote-up.svg"
        alt="Quote Image"
        width={30}
        height={30}
        style={{ marginTop: "5px" }}
      />
      <Box sx={{ marginTop: "10px" }}>
        <Typography
          variant="body2"
          color={theme.palette.text.primary}
          align="center"
          fontWeight={600}
        >
          {data.todaysQuote.quote.substring(0, 130)}
        </Typography>
        <Box
          sx={{
            height: "2px",
            width: "30%",
            background: theme.palette.text.green,
            marginY: "3px",
            marginX: "auto",
          }}
        />
        <Typography
          align="center"
          sx={{
            fontSize: "12px",
            fontStyle: "italic",
            lineHeight: "1.5",
            color: theme.palette.text.secondary,
           
          }}
        >
          {data.todaysQuote.author}
        </Typography>
      </Box>

      <Image
        src="/quote-up.svg"
        alt="Quote Image"
        width={30}
        height={30}
        style={{
          transform: "rotate(180deg)",
          float: "right",
        }}
      />
    </Box>
  );
};
