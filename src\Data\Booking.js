
import { gql } from "@apollo/client";
import { baseUrl } from "./ApolloClient";

import axios from "axios";
import formData from 'form-data';

export const MutationCreateBooking = gql`
mutation CreateBooking(
    # $id: Int!
    $title: String!
    $user_id: Int!
    # $resourceId: Int!
    $details: String!
    $location: String
    $teaBoy: Boolean!
    $parking: Boolean!
    $itTechnician: Boolean!
    $start: DateTime!
    $uid:String!
    # $status:String!
    $end: DateTime!
  ) {
    createBooking(CreateBookingInput: {
      # id: $id
      title: $title
      user_id: $user_id
      teaBoy: $teaBoy
      location: $location
      parking: $parking
      itTechnician: $itTechnician
      details: $details
      uid: $uid
      start: $start
      end: $end
    }) {
      id,
      title,
      user_id,
      details,
      teaBoy,
      location,
      parking,
      uid,
      itTechnician,
      start,
      end
    }
  }
  `;

export const createBooking = async (FormData, isImageChanged) => {
  try {
    let data = new formData();
    console.log(FormData);
    if (isImageChanged) {
      data.append('registrationDoc', FormData.registrationDoc)
    }
    data.append('title',FormData.title);
    data.append('user_id',FormData.user_id);
    data.append('teaBoy',FormData.teaBoy);
    data.append('location',FormData.location);
    data.append('parking',FormData.parking);
    data.append('itTechnician',FormData.itTechnician);
    data.append('details',FormData.details);
    data.append('uid',FormData.uid);
    data.append('start',FormData.start);
    data.append('end',FormData.end);

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${baseUrl}/${'our-booking'}`,
      // url: `http://localhost:3001/our-booking`,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: data
    };

    let res = await axios.request(config);
    console.log(res.data);
    return res.data;

  } catch (error) {
    console.log(error);
    return error.message
  }
}


export const getBookings = gql`
  query {
    bookings {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
  }
`;
// export const getBookings = gql`
//   query {
//     bookings {
//         id,
//         title,
//         user_id,
//         resourceId,
//         startTime,
//         endTime
//     }
//   }
// `;

export const GET_BOOKINGS_OF_USER = gql`
  query BookingsOfUser($user_id: Int!) {
    bookingsOfUser(id: $user_id) {
      id
      title
      start
      end
      details
      parking
      teaBoy
      location
      itTechnician
      registrationDoc
      uid
      user_id
    }
  }
`;

export const GET_BOOKINGS_OF_TEA_BOY = gql`
  query {
    bookingsOfTeaBoy {
      id
      title
      start
      end
      details
      registrationDoc
      parking
      teaBoy
      location
      itTechnician
      uid
      user_id
    }
  }
`;

export const mutationUpdateBookingStatus = gql`
  mutation UpdateBookingStatus(
    $id: Int!
    $status: String!
  ) {
    updateBookingStatus(updateBookingInput: {
        id: $id
        status: $status
    }) {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
}
`;