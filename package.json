{"name": "my-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3008", "build": "next build", "start": "next start -p 3008", "lint": "next lint"}, "dependencies": {"@aldabil/react-scheduler": "^2.7.26", "@apollo/client": "^3.13.8", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@heroicons/react": "^1.0.6", "@mui/icons-material": "^5.14.16", "@mui/lab": "^5.0.0-alpha.152", "@mui/material": "^5.16.7", "@mui/system": "^5.14.18", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.3", "apollo-upload-client": "^18.0.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "fs": "^0.0.1-security", "graphql": "^16.11.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "next": "^14.0.1", "next-auth": "^4.24.5", "next-cookies": "^2.0.3", "react": "^18", "react-calendar": "^4.6.1", "react-dom": "^18", "react-dropzone": "^14.2.10", "react-scheduler": "^0.1.0", "react-slick": "^0.29.0", "recharts": "^2.9.3", "sharp": "^0.33.0", "slick-carousel": "^1.8.1", "swiper": "^11.0.4"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.1", "tailwindcss": "^3.3.5"}}