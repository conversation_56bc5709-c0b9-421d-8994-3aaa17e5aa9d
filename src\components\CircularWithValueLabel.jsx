import * as React from 'react';
import Image from 'next/image';
import PropTypes from 'prop-types';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function CircularProgressWithLabel(props) {
    return (
        <Box sx={{ position: 'relative', display: 'inline-flex'}}>
          <CircularProgress  variant="determinate" {...props} size={70} thickness={3} sx={{color:"#00BC82"}}/>
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {/* <Typography variant="h6" component="div" color="text.secondary">
              {`${Math.round(props.value)}%`}
            </Typography> */}
            <Image src="/icons/task-square.png" alt="task-square.png" />
          </Box>
        </Box>
    );
}

CircularProgressWithLabel.propTypes = {
    /**
     * The value of the progress indicator for the determinate variant.
     * Value between 0 and 100.
     * @default 0
     */
    value: PropTypes.number.isRequired,
};

export default function CircularWithValueLabel(props) {
    const [progress, setProgress] = React.useState(70);

    return <CircularProgressWithLabel value={progress} {...props} />;
}