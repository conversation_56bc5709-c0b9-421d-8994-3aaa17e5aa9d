import Dashboard from "@/components/Dashboard";
import { Delete, Update } from "@mui/icons-material";
import { Grid, Tooltip } from "@mui/material";

import { getAllUsers, updateUser } from "@/Data/User";
import withAdminAuth from "@/components/auth/withAdminAuth";
import { PhotoCamera } from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Input,
  InputAdornment,
  MenuItem,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { useEffect, useState } from "react";
import DataTable from "./component/DataTable";
import { UpdateModal } from "./component/DialogBox";
import SnackbarComponent from "@/components/SnackBar";

const columns = [
  { id: "name", label: "User Name", minWidth: 150 },
  {
    id: "user_principal_name",
    label: "User Principal",
    minWidth: 150,
    align: "left",
  },
  { id: "email", label: "Email", minWidth: 150, align: "left" },
  { id: "designation", label: "Designation", minWidth: 130, align: "center" },
  { id: "department", label: "Department", minWidth: 130, align: "center" },
  { id: "gender", label: "Gender", minWidth: 80, align: "center" },
  { id: "role", label: "Role", minWidth: 80, align: "center" },
  { id: "status", label: "Status", minWidth: 80, align: "center" },
  { id: "new_joiner", label: "New Joiner", minWidth: 110, align: "center" },
];

// const rows = [
//     { name: "ahhd", dn: "www.abc.com", status: "active", role: "user" },
//     { name: "fahad", dn: "www.abc.com", status: "active", role: "admin" },
//     { name: "saad", dn: "www.abc.com", status: "active", role: "user" },
//     { name: "mahad", dn: "www.abc.com", status: "active", role: "user" },
//     { name: "asad", dn: "www.abc.com", status: "active", role: "user" },
// ];

const AddUser = (props) => {
  const {
    data,
    setOpenUpdate,
    opt,
    users,
    setUsers,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
  } = props;
  const [id, setId] = useState(data && opt == "update" ? data.id : "");
  const [profile, setProfile] = useState(
    data && data.profile ? data.profile : ""
  );
  const [principal, setPrincipal] = useState(
    data ? data.user_principal_name : ""
  );
  const [email, setEmail] = useState(data && data.email ? data.email : "");
  const [name, setName] = useState(data && data.name ? data.name : "");
  const [nameArabic, setNameArabic] = useState(
    data && data.name_arabic ? data.name_arabic : ""
  );
  const [designation, setDesignation] = useState(
    data && data.designation ? data.designation : ""
  );
  const [designationArabic, setDesignationArabic] = useState(
    data && data.designation_arabic ? data.designation_arabic : ""
  );
  const [department, setDepartment] = useState(
    data && data.department ? data.department : ""
  );
  const [departmentArabic, setDepartmentArabic] = useState(
    data && data.department_arabic ? data.department_arabic : ""
  );
  const [gender, setGender] = useState(data && data.gender ? data.gender : "");
  const [role, setRole] = useState(data ? data.role : "");
  const [status, setStatus] = useState(data && data ? data.status : true);
  const [newJoining, setNewJoining] = useState(
    data && data ? data.new_joiner : true
  );
  const [bioLink, setBioLink] = useState(
    data && data.bio_link ? data.bio_link : ""
  );
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [image, setImage] = useState(profile);

  const theme = useTheme();

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      console.log(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result);
        setProfile(file);
      };
      reader.readAsDataURL(file);
    }
    setIsImageChanged(true);
    console.log("isimagechange:", isImageChanged);
  };

  const updateHandler = () => {
    return new Promise(async (res, rej) => {
      let fieldsData = {
        id: id,
        profile: profile,
        name: name,
        nameArabic: nameArabic,
        principal: principal,
        email: email,
        designation: designation,
        designationArabic: designationArabic,
        department: department,
        departmentArabic: departmentArabic,
        gender: gender,
        role: role,
        status: status,
        newJoining: newJoining,
        bioLink: bioLink,
      };
      console.log(fieldsData);
      // if (opt != "update") {
      //   let response = await createNews(fieldsData);
      //   console.log(response);
      //   if (response?.code == 200) {
      //     setNews((prevNews) => [...prevNews, response.data]);
      //     setSnackbarMessage(`News added successfully`);
      //     setSnackbarSeverity("success");
      //     setSnackbarOpen(true);
      //   } else {
      //     console.log(response?.error);
      //     rej(response?.error);
      //     setSnackbarMessage(`Failed to add news`);
      //     setSnackbarSeverity("error");
      //     setSnackbarOpen(true);
      //   }
      // } else {
      let response = await updateUser(fieldsData, isImageChanged);
      // console.log(response);
      if (response?.code == 200) {
        setUsers((prevUser) =>
          prevUser.map((user) =>
            // console.log("user after succes: ", user);
            user.id === data.id ? { ...user, ...response.data } : user
          )
        );

        setSnackbarMessage(`User updated successfully`);
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      } else {
        console.log(response?.error);
        rej(response?.error);
        setSnackbarMessage(`Failed to update user`);
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
      setOpenUpdate(false);
      res(data);
    });
  };

  const handleImageRemove = () => {
    setImage(null);
  };

  return (
    <>
      {/* <Typography variant="body2">Add News</Typography> */}

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Input
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          endAdornment={
            // <InputAdornment position="end">
            <Tooltip title="Upload Image">
              <IconButton
                color="secondary"
                component="label"
                htmlFor="imageInput"
              >
                <PhotoCamera />
              </IconButton>
            </Tooltip>
            // </InputAdornment>
          }
          inputProps={{ id: "imageInput", style: { display: "none" } }}
        />
        {image && (
          <Box
            sx={{
              margin: "auto",
              display: "flex",
              flexDirection: "column",
              gap: 1,
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                border: "5px solid purple",
                borderRadius: "50px",
                width: "100px",
                height: "100px",
                overflow: "hidden",
              }}
            >
              <img
                src={image}
                alt="Selected"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
              />
            </Box>
            <Tooltip title="Remove Image">
              <Button
                color="secondary"
                startIcon={<Delete />}
                onClick={handleImageRemove}
              >
                Remove
              </Button>
            </Tooltip>
          </Box>
        )}
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="name"
            fullWidth
            label="Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={false} // or true based on your requirement
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="arabicName"
            fullWidth
            label="Arabic Name"
            value={nameArabic}
            onChange={(e) => setNameArabic(e.target.value)}
            disabled={false}
            inputProps={{ style: { textAlign: "right", direction: "rtl" } }}
          />
        </Grid>
      </Grid>

      <TextField
        margin="normal"
        size="small"
        id="name"
        fullWidth
        label="User Principal Name"
        value={principal}
        onChange={(e) => setPrincipal(e.target.value)}
        disabled
      />

      <TextField
        margin="normal"
        size="small"
        id="email"
        fullWidth
        label="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />

      {/* designation & designation_arabic */}
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="designation"
            fullWidth
            label="Designation"
            value={designation}
            onChange={(e) => setDesignation(e.target.value)}
            disabled={false}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="designation_arabic"
            fullWidth
            label="Designation in Arabic"
            value={designationArabic}
            onChange={(e) => setDesignationArabic(e.target.value)}
            disabled={false}
            inputProps={{ style: { textAlign: "right", direction: "rtl" } }}
          />
        </Grid>
      </Grid>

      {/* department & department_arabic */}
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="department"
            fullWidth
            label="Department"
            value={department}
            onChange={(e) => setDepartment(e.target.value)}
            disabled={false}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="department_arabic"
            fullWidth
            label="Department in Arabic"
            value={departmentArabic}
            onChange={(e) => setDepartmentArabic(e.target.value)}
            disabled={false}
            inputProps={{ style: { textAlign: "right", direction: "rtl" } }}
          />
        </Grid>
      </Grid>

      {/* role & gender */}
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="role"
            select
            label="Role"
            fullWidth
            helperText="Please select Role"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            SelectProps={{
              MenuProps: {
                PaperProps: {
                  style: {
                    backgroundColor: theme.palette.background.primary,
                  },
                },
              },
            }}
          >
            <MenuItem value="USER">USER</MenuItem>
            <MenuItem value="ADMIN">ADMIN</MenuItem>
            <MenuItem value="TEA-BOY">TEA BOY</MenuItem>
            <MenuItem value="PARKING">PARKING</MenuItem>
            <MenuItem value="ROOM">ROOM</MenuItem>
            <MenuItem value="IT-TECHNICIAN">IT-TECHNICIAN</MenuItem>
          </TextField>
        </Grid>
        <Grid item xs={6}>
          <TextField
            margin="normal"
            size="small"
            id="gender"
            select
            label="Gender"
            fullWidth
            helperText="Please select Gender"
            value={gender}
            onChange={(e) => setGender(e.target.value)}
            SelectProps={{
              MenuProps: {
                PaperProps: {
                  style: {
                    backgroundColor: theme.palette.background.primary,
                  },
                },
              },
            }}
          >
            <MenuItem value="male">Male</MenuItem>
            <MenuItem value="female">Female</MenuItem>
          </TextField>
        </Grid>
      </Grid>

      {/* status & new_joining */}
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            select
            id="status"
            size="small"
            label="Status"
            fullWidth
            helperText="Please select status"
            value={status}
            margin="normal"
            onChange={(e) => setStatus(e.target.value)}
            SelectProps={{
              MenuProps: {
                PaperProps: {
                  style: {
                    backgroundColor: theme.palette.background.primary,
                  },
                },
              },
            }}
          >
            <MenuItem value="true">active</MenuItem>
            <MenuItem value="false">Inactive</MenuItem>
          </TextField>
        </Grid>

        <Grid item xs={6}>
          <TextField
            select
            id="new_joiner"
            size="small"
            label="New Joining"
            fullWidth
            helperText="Please select status"
            value={newJoining}
            margin="normal"
            onChange={(e) => setNewJoining(e.target.value)}
            SelectProps={{
              MenuProps: {
                PaperProps: {
                  style: {
                    backgroundColor: theme.palette.background.primary,
                  },
                },
              },
            }}
          >
            <MenuItem value="true"> On </MenuItem>
            <MenuItem value="false"> Off </MenuItem>
          </TextField>
        </Grid>
      </Grid>

      <TextField
        margin="normal"
        size="small"
        id="bio_link"
        fullWidth
        label="Bio Link"
        value={bioLink}
        onChange={(e) => setBioLink(e.target.value)}
      />

      <Button onClick={updateHandler}>Update</Button>
    </>
  );
};

const Users = () => {
  // const [open, setOpen] = useState(false);
  const [Data, setData] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [openUpdateBox, setOpenUpdateBox] = useState(false);
  const [index, setIndex] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbarOpen(false);
  };

  useEffect(() => {
    if (!loading && !error) {
      const fetchNews = async () => {
        try {
          setLoading(true);
          let usersData = await getAllUsers();
          setUsers(usersData);
        } catch (error) {
          console.log(error);
          setError(error);
        } finally {
          setLoading(false);
        }
      };
      fetchNews();
    }
  }, []);

  return (
    <>
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          <Typography variant="h5" sx={{ marginY: "10px" }}>
            Users
          </Typography>
          <UpdateModal
            title={"Update User"}
            comp={
              <AddUser
                data={Data}
                setOpenUpdate={setOpenUpdateBox}
                users={users}
                opt={"update"}
                setUsers={setUsers}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
              />
            }
            btn={false}
            openUpdate={openUpdateBox}
            setOpenUpdate={setOpenUpdateBox}
          />
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress color="secondary" />
            </Box>
          ) : (
            <>
              {users?.length > 0 ? (
                <Box sx={{ maxWidth: "100%", overflow: "auto" }}>
                  <DataTable
                    columns={columns}
                    rows={users}
                    setData={setData}
                    setOpen={setOpenUpdateBox}
                    updateKey={"name"}
                    setIndex={setIndex}
                  />
                </Box>
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Typography variant="h5" sx={{ marginY: "10px" }}>
                    No data found
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
        <SnackbarComponent
          open={snackbarOpen}
          handleClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          message={snackbarMessage}
        />
      </Dashboard>
    </>
  );
};
export default withAdminAuth(Users);
