import React from "react";
import Dashboard from "@/components/Dashboard";
import Schedule2 from "@/components/Schedule2";
import { Box, Typography } from "@mui/material";
import withAuth from "@/components/auth/withAuth";

const AllocatorComponent = () => {
  return (
    <Box sx={{ marginTop: "25px", marginX: "25px" }}>
      <Typography sx={{ marginBottom: "15px" }}>
        Stay organized with our calendar—book tea boys, parking, and more.
      </Typography>
      <Box
        sx={{
          height: "2px",
          width: "15%",
          background: "#00BC82",
          marginTop: "-10px",
        }}
      ></Box>
      <Typography
        variant="h5"
        sx={{ fontWeight: "600", marginTop: "15px", marginBottom: "20px" }}
      >
        Schedule Meeting
      </Typography>
      <Schedule2 />
    </Box>
  );
};

const Allocator = () => {
  return (
    <Dashboard>
      <AllocatorComponent />
    </Dashboard>
  );
};

export default withAuth(Allocator);
