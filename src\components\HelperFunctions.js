import {
  FiberManualRecord,
  FiberManualRecordOutlined,
} from "@mui/icons-material";
import { useTheme } from "@mui/material";
import dayjs from "dayjs";

export const useSelectedColor = (color) => {
  // console.log("useSelectedColor", color)
  const theme = useTheme();
  if (color == "purple") {
    console.log("purple", theme.palette.purple.main);
    return theme.palette.purple.main;
  } else if (color == "blue") {
    console.log("blue", theme.palette.blue.main);
    return theme.palette.blue.main;
  } else if (color == "green") {
    console.log("green", theme.palette.green.main);
    return theme.palette.green.main;
  }
  return theme.palette.background.primary;
};
export const monthName = (monthNumber) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  return months[monthNumber];
};
export const getFormattedDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Month is zero-based
  const day = String(today.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};
export const getCurrentLocalTime = () => {
  const currentDate = new Date();
  const options = { hour: "numeric", minute: "numeric", hour12: true };
  const currentLocalTime = currentDate.toLocaleTimeString(undefined, options);
  return currentLocalTime;
};
export const getDateFromPicker = (date) => {
  if (typeof date != "string") {
    return new Date(date.$d).toISOString();
  } else {
    return new Date(date);
  }
};

// export const getDate = (date) => {
//   if (typeof date != "string") {
//     return new Date(date.$d).setHours(0, 0, 0, 0).toISOString();
//   } else {
//     return new Date(date);
//   }
// };

export const getDate = (selectedDate) => {
  if (typeof selectedDate !== "string") {
    const date = String(selectedDate.$D).padStart(2, '0');;
    const month = String(selectedDate.$d.getMonth() + 1).padStart(2, '0');
    const year = selectedDate.$y;
    const stringDate = `${year}-${month}-${date}`;
    console.log("stringDate in handler > ", stringDate);
    // console.log("date in helper functin", dayjs(date.$d).startOf('day').add(5, 'hour').toISOString()); // Logs the ISO string with time set to 00:00:00
    return stringDate; 
  } else {
    return selectedDate;
  }
};

export const getSortedAnnouncements = (data) => {
  const todaysDate = new Date().setHours(0, 0, 0, 0);
  let previousAnnouncements = [];
  let currentAnnouncements = [];

  data &&
    data.length !== 0 &&
    data.forEach((element) => {
      if (new Date(element.visibility) >= todaysDate) {
        currentAnnouncements.push(element);
      } else {
        previousAnnouncements.push(element);
      }
    });

  // Sort currentAnnouncements by updatedAt from newest to oldest
  currentAnnouncements.sort(
    (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
  );

  // Sort previousAnnouncements by updatedAt from newest to oldest
  previousAnnouncements.sort(
    (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
  );

  return [currentAnnouncements, previousAnnouncements];
};

export const formatDateTimeUTC = (utcDateTimeString) => {
  const options = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
    // timeZone: "UTC",
  };

  const date = new Date(utcDateTimeString);

  const formattedDateTime = new Intl.DateTimeFormat("en-US", options).format(
    date
  );

  return formattedDateTime;
};

// console.log(formatDateTimeUTC("2024-01-09T19:00:00.000Z"));

export const formatedValue = (columnId, value) => {
  if (columnId == "typeOfLeave") {
    return value == "casual" ? "annual" : "sick";
  }
  if (["visibility", "date", "start", "end", "updatedAt"].includes(columnId)) {
    return formatDateTimeUTC(value);
  } else if (columnId == "status") {
    if (value == true || value == "true") {
      return "Active";
    } else if (value == false || value == "false") {
      return "Inactive";
    } else {
      return value;
    }
  } else if (columnId == "new_joiner") {
    if (value == true || value == "true") {
      return <FiberManualRecord color="green" />; // Unfilled dot
    } else if (value == false || value == "false") {
      return <FiberManualRecordOutlined />;
    }

    //   // if (value == "true" || value == "false") {
    //   if (value) {
    //     // console.table([value,typeof(value)])
    //   } else {
    //   }
    // } else {
    //   return value;
    // }
  } else {
    return value;
  }
};

export const separateLatestData = (dataArray) => {
  // Clone the array to avoid mutating the original data
  let clonedArray = [...dataArray];

  // Sort the array based on the 'created_on' field in descending order
  clonedArray.sort((a, b) => new Date(b.created_on) - new Date(a.created_on));

  // Separate the latest item
  let latestItem = clonedArray[0];

  // Remove the first item (latest) from the array
  let remainingItems = clonedArray.slice(1);

  // Return the array in the desired structure
  return [latestItem, remainingItems];
};

export function getCurrentTime() {
  return new Date().toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });
}

export const formatTime = (dateTimeStr) => {
  const dateTime = new Date(dateTimeStr);
  return dateTime.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// export const mergeArrays = (objA, objB) => {
//   const mergedArray = [];

//   for (const itemA of objA) {
//     for (const itemB of objB) {
//       if (itemA.uid === itemB.uid) {
//         console.log(itemA, itemB);
//         mergedArray.push({
//           ...itemA,
//           id: itemB.id,
//           parking: itemB.parking,
//           teaBoy: itemB.teaBoy,
//           iTTechnician: itemB.iTTechnician,
//         })
//         break;
//       }
//       console.log(mergedArray);
//     }
//     mergedArray.push(itemA);
//   }

//   return mergedArray;
// }

// export const mergeArrays = (objA, objB) => {
//   const mergedArray = [];

//   for (const itemA of objA) {
//     const matchingItemB = objB.find(itemB => itemA.uid === itemB.uid);

//     if (matchingItemB) {
//       console.log(itemA, matchingItemB);
//       mergedArray.push({
//         ...itemA,
//         id: matchingItemB.id,
//         parking: matchingItemB.parking,
//         teaBoy: matchingItemB.teaBoy,
//         iTTechnician: matchingItemB.iTTechnician,
//       });
//     } else {
//       console.log(itemA);
//       mergedArray.push(itemA);
//     }
//   }

//   return mergedArray;
// }

export const mergeArrays = (arrayA, arrayB) => {
  // console.log(arrayA, arrayB);
  if (!Array.isArray(arrayA) || !Array.isArray(arrayB)) {
    throw new Error("Invalid input: both arguments must be arrays.");
  }

  const mergedArray = [];

  for (const itemA of arrayA) {
    const matchingItemB = arrayB.find((itemB) => itemA.uid === itemB.uid);

    if (matchingItemB) {
      mergedArray.push({
        ...itemA,
        ...matchingItemB,
      });
    } else {
      mergedArray.push(itemA);
    }
  }

  return mergedArray;
};

export function convertUtcToLocal(utcDateTimeString) {
  const utcDate = new Date(utcDateTimeString);

  let hours = utcDate.getHours();
  const minutes = utcDate.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'

  const minutesStr = minutes < 10 ? "0" + minutes : minutes;
  const localTime = `${hours}:${minutesStr} ${ampm}`;

  return localTime;
}
