import { Box, Typography, Grid, Divider } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import { useTheme } from "@mui/material/styles";
import { useQuery } from "@apollo/client";
import { getUserLeave } from "@/Data/Leave";
import { useSession } from "next-auth/react";

const LeaveTracker = () => {
  const theme = useTheme();
  const { data: session } = useSession();
  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
  } = useQuery(getUserLeave, {
    variables: { Id: session?.user?.id },
  }) || {};

  const leavetrackerdata = [
    {
      Leave: queryData ? queryData.getUserLeaves.casual : 10,
      totalLeave: 12,
      heading: "Casual",
      color: "#A665E1",
      size: 200,
      thickness: 3,
    },
    {
      Leave: queryData ? queryData.getUserLeaves.medical : 5,
      totalLeave: 7,
      heading: "Medical",
      color: "#62B6F3",
      size: 160,
      thickness: 4,
    },
    {
      Leave: queryData ? queryData.getUserLeaves.emergency : 3,
      totalLeave: 4,
      heading: "Emergency",
      color: "#52C41A",
      size: 110,
      thickness: 5,
    },
  ];

  return (
    <Grid
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
      sx={{
        minHeight: 495,
        background: theme.palette.background.secondary,
        borderRadius: "10px",
        boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
        padding: "20px",
      }}
    >
      {/* Header */}
      <Grid item xs={12} container justifyContent="space-between" 
      sx={{cursor: "default"}}>
        <Typography variant="h6" component="h2" fontSize="1.3rem" fontWeight="700">
          Leaves Tracker
        </Typography>
        <Typography
          variant="body1"
          sx={{ color: theme.palette.primary.main, cursor: "default" }}
        >
          {/* View all */}
        </Typography>
      </Grid>

      <Divider sx={{ width: '100%', marginY: '20px', background: theme.palette.text.secondary }} />
      <Box
        position="relative"
        sx={{ width: 200, height: 200, display: 'flex', transform: 'rotate(180deg)', justifyContent: 'center', paddingY: '140px', alignItems: 'center' }}
      >
        {leavetrackerdata.map((item, index) => {
          const percentage = (item.Leave / item.totalLeave) * 100;
          return (
            <CircularProgress
              key={index}
              variant="determinate"
              value={percentage}
              size={item.size}
              thickness={item.thickness}
              sx={{
                position: "absolute",
                color: item.color,
              }}
            />
          );
        })}
      </Box>

      {/* Leave Types */}
      <Grid item xs={12} container justifyContent="space-evenly" spacing={2} sx={{ marginTop: "20px" }}>
        {leavetrackerdata.map((item, index) => (
          <Box key={index} sx={{ textAlign: "center", width: "33%" }}>
            {/* Colored Dot */}
            <Box
              sx={{
                width: 8,
                height: 8,
                backgroundColor: item.color,
                borderRadius: "50%",
                display: "inline-block",
                marginRight: "4px",
              }}
            />
            {/* Leave Count */}
            <Typography variant="h4" fontWeight="bold" display="inline">
              {item.Leave}
              <Typography
                component="span"
                variant="subtitle1"
                sx={{ color: theme.palette.text.secondary }}
              >
                /{item.totalLeave}
              </Typography>
            </Typography>

            {/* Leave Type */}
            <Typography variant="subtitle2" fontWeight="600" color="text.secondary">
              {item.heading} Leaves
            </Typography>
          </Box>
        ))}
      </Grid>
    </Grid>
  );
};

export default LeaveTracker;
