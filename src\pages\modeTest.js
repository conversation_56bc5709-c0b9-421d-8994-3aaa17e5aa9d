import Celebration from "@/components/Celebration";
import Dashboard from "@/components/Dashboard";
import { Box, Typography, useTheme } from "@mui/material";

const ModeTest = () => {
    const theme = useTheme();
    return <>
        <Box sx={{ background: theme.palette.background.primary }}>
            <Typography variant="h1" color={theme.palette.text.primary}>hello</Typography>
            {/* <Dashboard></Dashboard> */}
            <Celebration/>
        </Box>
    </>
}

export default ModeTest;