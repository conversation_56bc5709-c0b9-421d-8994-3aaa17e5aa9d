import React, { useEffect, useState } from "react";
// import { gql , useQuery, useMutation } from '@apollo/client';
import { Scheduler } from "@aldabil/react-scheduler";
import {
  Typo<PERSON>,
  Box,
  Button,
  TextField,
  CircularProgress,
  Tab,
  Field,
} from "@mui/material";
import {
  MeetingRoom,
  CalendarMonth,
  CalendarViewDay,
} from "@mui/icons-material";
// import { Box, Typography } from "@mui/material";
// import "react-big-schedule/dist/css/style.css";
// import { queryGetRooms, queryGetMeetings, mutationCreateMeetings, CREATE_POST, mutationUpdateMeetings, mutationDeleteMeetings } from '../../graphql/queries';
import { TabContext, TabList, TabPanel } from "@mui/lab";
// import StickyHeadTable from 'src/components/StickyHeadTable';

const BookRoom = () => {
  // const [createPost] = useMutation(CREATE_POST);
  // const [createMeeting] = useMutation(mutationCreateMeetings);
  // const [updateMeeting] = useMutation(mutationUpdateMeetings);
  // const [deleteMeeting] = useMutation(mutationDeleteMeetings);
  // const meetingQuery = useQuery(queryGetMeetings);
  // const roomQuery = useQuery(queryGetRooms);

  const handleConfirm = async (event, action) => {
    // console.log("created");
    return new Promise(async (res, rej) => {
      let Gid = event.event_id;

      let Data = {
        variables: {
          id: event.event_id,
          title: event.title,
          room: event.room,
          description: event.description,
          start: new Date(event.start).toISOString(),
          end: new Date(event.end).toISOString(),
          color: event.color,
        },
      };

      // console.log(action);
      if (action === "edit") {
        /** PUT event to remote DB */
        updateMeeting(Data)
          .then((response) => {
            console.log(response.data);
          })
          .catch((error) => {
            console.error(error);
            rej(error);
          });

        // console.log("handleConfirm =", action, event.title);
        // console.log(Data);
        res({ ...event });

        // console.log("in edit :" + action);
      } else if (action === "create") {
        /**POST event to remote DB */
        createMeeting(Data)
          .then((response) => {
            console.log(response.data);
            Gid = response.data.createMeeting.data.id;

            // console.log("id : " + response.data.createMeeting.data.id);
          })
          .catch((error) => {
            console.error(error);
          });

        // console.log("handleConfirm =", action, event.title);
        // console.log(Data);
        // console.log({ ...event, event_id: Gid });
        res({ ...event, event_id: Gid });

        // console.log("in create :" + action)
      }
      rej("Ops... Faild");

      // const isFail = Math.random() > 0.6;
      // setTimeout(() => {
      //     if (isFail) {
      //         rej("Ops... Faild");
      //     } else {
      //         res({
      //             ...event,
      //             event_id: event.event_id || Math.random()
      //         });
      //     }
      // }, 3000);
    });
  };

  const handleDelete = async (deletedId) => {
    return new Promise((res, rej) => {
      // console.log("delete  : " + deletedId);
      deleteMeeting({ variables: { id: deletedId } })
        .then((response) => {
          res(response.data);
        })
        .catch((error) => {
          rej(error);
        });

      // setTimeout(() => {
      //     console.log(deletedId);
      //     res(deletedId);
      // }, 3000);
    });
  };

  const formatDate = (date) => {
    const isoDate = new Date(date);
    const formattedDate = `${isoDate.getFullYear()}/${(isoDate.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${isoDate
      .getDate()
      .toString()
      .padStart(2, "0")} ${isoDate
      .getHours()
      .toString()
      .padStart(2, "0")}:${isoDate.getMinutes().toString().padStart(2, "0")}`;

    return formattedDate;
  };

  const getRooms = () => {
    let room = [];
    let error;

    if (roomQuery.loading) {
      console.log("Loading...");
    } else if (roomQuery.error) {
      console.error("Error:", roomQuery.error);
      error = roomQuery.error;
    } else if (
      roomQuery.data &&
      roomQuery.data.rooms &&
      roomQuery.data.rooms.data
    ) {
      room = roomQuery.data.rooms.data.map((item) => {
        return {
          id: +item.id,
          text: item.attributes.Name,
          value: item.id,
          loc: item.attributes.location,
          description: item.attributes.description,
        };
      });
    } else {
      console.log("Data not available yet");
    }

    return { room, error };
  };

  const getMeetings = () => {
    let meetings = [];
    let error;

    // const { loading, error, data } = useQuery(queryGetMeetings);
    if (meetingQuery.loading) {
      console.log("Loading...");
    } else if (meetingQuery.error) {
      console.error("Error:", meetingQuery.error);
      error = meetingQuery.error;
    } else if (
      meetingQuery.data &&
      meetingQuery.data.meetings &&
      meetingQuery.data.meetings.data
    ) {
      meetings = meetingQuery.data.meetings.data.map((item) => {
        return {
          event_id: +item.id,
          title: item.attributes.title,
          start: new Date(formatDate(item.attributes.start)),
          end: new Date(formatDate(item.attributes.end)),
          color: item.attributes.color,
          room: item.attributes.room,
        };
      });
    } else {
      console.log("Data not available yet");
    }
    console.log(meetings);

    return { meetings, error };
  };

  const commentsClickHandler = () => {
    console.log({
      variables: {
        title: "asdsda",
        content: "aasdasddsasdasasdadsa",
        waqt: "2023-10-17T05:00:00.000z",
      },
    });
    createPost({
      variables: {
        title: "asdsda",
        content: "aasdasddsasdasasdadsa",
        waqt: "2023-10-17T05:00:00.000Z",
      },
    })
      .then((response) => {
        console.log(response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };
  // let { room } = getRooms();
  // let { meetings } = getMeetings();

  // console.log(new Date("2023/10/17 12:30"));

  // console.log(meetings);
  //-------------------------------------------------------------------------------------

  const [value, setValue] = React.useState("1");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  //----------------------------------------------------
  // useEffect(() => {

  // }, []);
  const columns = [
    { id: "text", label: "Title", minWidth: 170 },
    {
      id: "room",
      label: "Room",
      minWidth: 100,
      format: (value) => "Room " + value,
    },
    { id: "start", label: "start", minWidth: 100 },
    { id: "end", label: "end", minWidth: 100 },

    // { id: 'start', label: 'start', minWidth: 100, format: (value) => formatDate(value)},
    // { id: 'end', label: 'end', minWidth: 100, format: (value) => formatDate(value) },
    {
      id: "description",
      label: "description",
      minWidth: 410,
      align: "left",

      // format: (value) => value.toLocaleString('en-US'),
    },
  ];

  const rows = [
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/12 09:30"),
      end: new Date("2023/11/12 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/13 09:30"),
      end: new Date("2023/11/13 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/14 09:30"),
      end: new Date("2023/11/14 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/15 09:30"),
      end: new Date("2023/11/15 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/16 09:30"),
      end: new Date("2023/11/16 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/17 09:30"),
      end: new Date("2023/11/17 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/18 09:30"),
      end: new Date("2023/11/18 10:30"),
    },
    {
      event_id: 1,
      title: "Event 1",
      start: new Date("2023/11/19 09:30"),
      end: new Date("2023/11/19 10:30"),
    },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
    // { text: "abc", start: "11:00", end: "12:30", room: "2", description: "description" },
  ];

  return (
    <>
      {/* <Typography>Schedule Meeting</Typography> */}

      {/* <Button onClick={commentsClickHandler}>add comments</Button> */}
      <Box
        sx={{
          width: "100%",
          typography: "body1",
          maxHeight: "700px",
          overflow: "auto",
        }}
      >
        <TabContext value={value}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <TabList onChange={handleChange} aria-label="lab API tabs example">
              <Tab
                icon={<CalendarMonth />}
                iconPosition="start"
                label="Calender"
                value="1"
              />
              <Tab
                icon={<CalendarViewDay />}
                iconPosition="start"
                label="Grid"
                value="2"
              />
              {/* <Tab label="Item Three" value="3" /> */}
            </TabList>
          </Box>
          <TabPanel value="1">
            {/* {(room.length && meetings.length) > 0 ? ( */}
            <Scheduler
              // stickyNavigation={false}
              // locale={eng}
              view="week"
              day={{ startHour: 0, endHour: 24 }}
              // month={null}
              week={{ startHour: 0, endHour: 24 }}
              onConfirm={handleConfirm}
              onDelete={handleDelete}
              fields={[
                {
                  name: "room",
                  type: "select",
                  // options: room,
                  options: [
                    { id: 1, text: "John", value: 1, loc: 1 },
                    { id: 2, text: "Mark", value: 2, loc: 1 },
                  ],
                  config: {
                    label: "Room",
                    required: true,
                    errMsg: "Plz Select Room",
                  },
                },
                {
                  name: "description",
                  type: "input",
                  default: "Default Value...",
                  config: { label: "Details", multiline: true, rows: 4 },
                },
                {
                  name: "color",
                  type: "input",
                  default: "#e6ccff",
                  config: { label: "color", md: 6 },
                },
              ]}
              events={rows}
              // events={meetings}
              viewerExtraComponent={(fields, event) => {
                return (
                  <div style={{ display: "flex" }}>
                    {/* <MeetingRoom /> */}
                    <p
                      style={{
                        fontSize: "14px",
                        display: "block",
                        margin: "3px 6px",
                      }}
                      color="secondary"
                    >
                      {" "}
                      Room {event.room || "Nothing..."}
                    </p>
                  </div>
                );
              }}

              // events={[
              //     {
              //         event_id: 1,
              //         title: "Event 1",
              //         start: new Date("2023/10/18 09:30"),
              //         end: new Date("2023/10/18 10:30"),
              //         color: "rgb(244,344,223)"
              //     },
              // ]}
            />

            {/* ) : (
                            <Box sx={{ display: 'flex', justifyContent: "center" }}>
                                <CircularProgress color="secondary" /> */}

            {/* <div>Loading...</div> */}
            {/* </Box>
                        )} */}
          </TabPanel>
          {/* <TabPanel value="2">
                        <StickyHeadTable columns={columns} rows={rows} />
                        {console.log(meetings)}
                    </TabPanel> */}
          {/* <TabPanel value="3">Item Three</TabPanel> */}
        </TabContext>
      </Box>
    </>
  );
};

export default BookRoom;

// https://www.npmjs.com/package/@aldabil/react-scheduler
