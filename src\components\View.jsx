import { useMode } from "./ModeContext"

const { Box } = require("@mui/system")
const { default: Layout } = require("./Layout")
const { Toolbar } = require("@mui/material")

const View = ({ children }) => {
    const { mode } = useMode();
    
    return <Box
        component="main"
        sx={{
            backgroundColor: mode == "light" ? "#FCFAFF" : "#252525",
            // backgroundColor: theme.palette.background.primary,
            flexGrow: 1,
            minHeight: "100vh",
            width: children.mainDivWidth, // use t`he variable here
            margin: "auto",
            overflow: "auto",
        }}
    >
        <Toolbar />
        <Layout>
            {children}
        </Layout>
    </Box>
}

export default View;