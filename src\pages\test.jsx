import Dashboard from "@/components/Dashboard";
import { withAuthServerSideProps } from "@/components/auth/withAuthServerSide";
import React, { useState } from "react";

const Test = (() => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        biography: '',
        profilePicture: null
    });

    const handleChange = (e) => {
        const { name, value, files } = e.target;
        setFormData({
            ...formData,
            [name]: files ? files[0] : value
        });
    }

    const handleSubmit = (e) => {
        e.preventDefault();
        // Handle the form submission logic here
        console.log(formData);
    }

    return (
        <form onSubmit={handleSubmit}>
            <div>
                <label>Name:</label>
                <input type="text" name="name" value={formData.name} onChange={handleChange} />
            </div>
            <div>
                <label>Email:</label>
                <input type="email" name="email" value={formData.email} onChange={handleChange} />
            </div>
            <div>
                <label>Biography:</label>
                <textarea name="biography" value={formData.biography} onChange={handleChange} />
            </div>
            <div>
                <label>Profile Picture:</label>
                <input type="file" name="profilePicture" onChange={handleChange} />
            </div>
            <button type="submit">Submit</button>
        </form>
    );

})

const Allocator = () => {
    return <Dashboard>{<Test />} </Dashboard>;
};

export const getServerSideProps = withAuthServerSideProps();

export default Allocator;