import Dashboard from "@/components/Dashboard";
import { Box, MenuItem, TextField, Typography, Button, CircularProgress, useTheme } from "@mui/material";
import DataTable from "./component/DataTable";
import { getEvents, mutationCreateEvent, mutationRemoveEvent, mutationUpdateEvent } from "@/Data/Events";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { getDateFromPicker } from "@/components/HelperFunctions";
import SnackbarComponent from "@/components/SnackBar";
import withAuth from "@/components/auth/withAuth";
import withAdminAuth from "@/components/auth/withAdminAuth";

const columns = [
  { id: "title", label: "Title", minWidth: 170 },
  { id: "details", label: "Details", minWidth: 370, align: "left" },
  { id: "category", label: "Category", minWidth: 100, align: "left" },
  { id: "date", label: "Date", minWidth: 100, align: "center" },
  { id: "status", label: "Status", minWidth: 100, align: "center" },
];

const AddEvent = (props) => {
  const { 
    opt, 
    data, 
    events, 
    setEvents, 
    modalHandler, 
    setOpen, 
    setOpenUpdate, 
    setSnackbarOpen, 
    setSnackbarSeverity, 
    setSnackbarMessage 
  } = props;

  // Initialize state with proper default values
  const [id, setId] = useState(data && opt === "update" ? data.id : null);
  const [title, setTitle] = useState(data && opt === "update" ? data.title : "");
  const [date, setDate] = useState(
    data && opt === "update" ? dayjs(data.date) : dayjs()
  );
  const [category, setCategory] = useState(
    data && opt === "update" ? data.category : ""
  );
  const [status, setStatus] = useState(
    data && opt === "update" ? data.status.toString() : "true"
  );
  const [details, setDetails] = useState(
    data && opt === "update" ? data.details : ""
  );
  
  const [createEvent, { loading: createLoading }] = useMutation(mutationCreateEvent);
  const [updateEvent, { loading: updateLoading }] = useMutation(mutationUpdateEvent);
  const theme = useTheme();

  const [errors, setErrors] = useState({});

  // Update state when data prop changes (for update modal)
  useEffect(() => {
    if (data && opt === "update") {
      setId(data.id);
      setTitle(data.title || "");
      setDate(dayjs(data.date));
      setCategory(data.category || "");
      setStatus(data.status?.toString() || "true");
      setDetails(data.details || "");
    }
  }, [data, opt]);

  const validateForm = () => {
    let tempErrors = {};
    
    if (!title?.trim()) tempErrors.title = "Title is required.";
    if (!category) tempErrors.category = "Category is required.";
    if (!date || !dayjs(date).isValid()) tempErrors.date = "Valid date is required.";
    if (!status) tempErrors.status = "Status is required.";
    if (!details?.trim()) tempErrors.details = "Details are required.";
    
    // For updates, ensure ID exists and is valid UUID format
    if (opt === "update" && (!id || typeof id !== 'string')) {
      tempErrors.id = "Valid ID is required for updates.";
    }
    
    // Validate date is not in the past (optional - remove if not needed)
    if (date && dayjs(date).isValid() && dayjs(date).isBefore(dayjs(), 'day')) {
      // tempErrors.date = "Event date cannot be in the past."; // Uncomment if needed
    }
    
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const submitHandler = async () => {
    try {
      if (!validateForm()) {
        return;
      }

      // Prepare mutation data to match your GraphQL resolver structure
      let mutationData;
      
      if (opt === "update") {
        // For updateEvent mutation: separate id and updateEventInput
        mutationData = {
          variables: {
           id: String(id), // ID parameter for resolver
            updateEventInput: {
              // Remove the ID from updateEventInput - it might be causing conflicts
              title: String(title.trim()),
              category: String(category),
              date: dayjs(date).isValid() ? dayjs(date).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]') : dayjs().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'), // Try ISO string format
              status: Boolean(status === "true"),
              details: String(details.trim()),
            },
          },
        };
      } else {
        // For createEvent mutation: use createEventInput
        mutationData = {
          variables: {
            createEventInput: {
              title: String(title.trim()),
              category: String(category),
              date: dayjs(date).isValid() ? dayjs(date).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]') : dayjs().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'), // Try ISO string format
              status: Boolean(status === "true"),
              details: String(details.trim()),
            },
          },
        };
      }

      console.log("Mutation data being sent:", JSON.stringify(mutationData, null, 2));

      let response;
      if (opt === "update") {
        response = await updateEvent(mutationData);
        
        // Update local state
        const updatedEvent = response.data.updateEvent;
        const itemIndex = events.findIndex(item => item.id === id);
        
        if (itemIndex !== -1) {
          const updatedEvents = [...events];
          updatedEvents[itemIndex] = {
            ...updatedEvent,
            status: updatedEvent.status.toString(),
            date: updatedEvent.date,
          };
          setEvents(updatedEvents);
        }
        
        setOpenUpdate(false);
        setSnackbarMessage("Event updated successfully");
      } else {
        response = await createEvent(mutationData);
        
        // Add new event to local state
        const newEvent = response.data.createEvent;
        setEvents([{
          ...newEvent,
          status: newEvent.status.toString(),
        }, ...events]);
        
        setOpen(false);
        setSnackbarMessage("Event created successfully");
        
        // Reset form for create mode
        resetForm();
      }

      setSnackbarSeverity("success");
      setSnackbarOpen(true);

    } catch (error) {
      console.error("Mutation error:", error);
      console.error("GraphQL errors:", error.graphQLErrors);
      console.error("Network error:", error.networkError);
      console.error("Error message:", error.message);
      
      // Log the specific GraphQL error details
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        error.graphQLErrors.forEach((gqlError, index) => {
          console.error(`GraphQL Error ${index + 1}:`, {
            message: gqlError.message,
            locations: gqlError.locations,
            path: gqlError.path,
            extensions: gqlError.extensions
          });
        });
      }
      
      let errorMessage = "An error occurred";
      
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        errorMessage = error.graphQLErrors[0].message;
        // If it's a validation error, try to extract more details
        if (error.graphQLErrors[0].extensions) {
          console.error("Error extensions:", error.graphQLErrors[0].extensions);
        }
      } else if (error.networkError) {
        errorMessage = "Network error occurred";
        console.error("Network error details:", error.networkError);
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSnackbarMessage(`Failed to ${opt === "update" ? "update" : "create"} event: ${errorMessage}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  const resetForm = () => {
    setTitle("");
    setDate(dayjs());
    setCategory("");
    setStatus("true");
    setDetails("");
    setErrors({});
  };

  const isLoading = createLoading || updateLoading;

  return (
    <>
      <TextField
        error={!!errors.title}
        helperText={errors.title}
        margin="normal"
        size="small"
        id="Title"
        fullWidth
        label="Title"
        value={title}
        onChange={(e) => {
          setTitle(e.target.value);
          if (errors.title) {
            setErrors(prev => ({ ...prev, title: "" }));
          }
        }}
        disabled={isLoading}
      />
      
      <TextField
        error={!!errors.category}
        helperText={errors.category}
        id="category"
        select
        label="Category"
        fullWidth
        value={category}
        onChange={(e) => {
          setCategory(e.target.value);
          if (errors.category) {
            setErrors(prev => ({ ...prev, category: "" }));
          }
        }}
        disabled={isLoading}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
        sx={{ width: "100%", marginY: "10px" }}
      >
        <MenuItem value="corporate">Company Events</MenuItem>
        <MenuItem value="inperson">Celebrations</MenuItem>
      </TextField>
      
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DateTimePicker
          label="Date"
          value={date}
          onChange={(newValue) => {
            setDate(newValue);
            if (errors.date) {
              setErrors(prev => ({ ...prev, date: "" }));
            }
          }}
          disabled={isLoading}
          sx={{ 
            width: "100%", 
            marginY: "20px",
            '& .MuiFormHelperText-root': {
              color: errors.date ? theme.palette.error.main : 'inherit'
            }
          }}
          slotProps={{
            textField: {
              error: !!errors.date,
              helperText: errors.date,
            },
          }}
        />
      </LocalizationProvider>
      
      <TextField
        error={!!errors.status}
        helperText={errors.status}
        id="status"
        select
        label="Status"
        fullWidth
        value={status}
        onChange={(e) => {
          setStatus(e.target.value);
          if (errors.status) {
            setErrors(prev => ({ ...prev, status: "" }));
          }
        }}
        disabled={isLoading}
        sx={{
          background: theme.palette.background.secondary,
        }}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        <MenuItem value="true">Active</MenuItem>
        <MenuItem value="false">Inactive</MenuItem>
      </TextField>
      
      <TextField
        error={!!errors.details}
        helperText={errors.details}
        margin="normal"
        size="small"
        id="details"
        label="Details"
        multiline
        rows={6}
        placeholder="Enter event details..."
        fullWidth
        value={details}
        onChange={(e) => {
          setDetails(e.target.value);
          if (errors.details) {
            setErrors(prev => ({ ...prev, details: "" }));
          }
        }}
        disabled={isLoading}
      />
      
      <Button
        onClick={submitHandler}
        disabled={isLoading}
        style={{
          color: theme.palette.text.white,
          background: isLoading ? theme.palette.action.disabled : theme.palette.text.purple,
          marginTop: "16px",
          position: "relative",
        }}
        fullWidth
      >
        {isLoading ? (
          <>
            <CircularProgress size={20} color="inherit" style={{ marginRight: "8px" }} />
            {opt === "update" ? "Updating..." : "Creating..."}
          </>
        ) : (
          opt === "update" ? "Update Event" : "Create Event"
        )}
      </Button>
    </>
  );
};

const Event = () => {
  const [events, setEvents] = useState([]);
  const [index, setIndex] = useState(null);
  const [open, setOpen] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [data, setData] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
    refetch: refetchEvents
  } = useQuery(getEvents, {
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
  });

  const [removeEvent, { loading: deleteLoading }] = useMutation(mutationRemoveEvent);

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbarOpen(false);
  };

  const modalHandler = () => {
    setOpen(!open);
    if (!open) {
      setData(null); // Reset data when opening create modal
    }
  };

  const deleteHandler = async (deletedId, index) => {
    try {
      console.log("Deleting event with ID:", deletedId);
      
      // Match your resolver's removeEvent mutation signature
      const response = await removeEvent({ 
        variables: { 
          id: String(deletedId) // ID parameter for removeEvent resolver
        }
      });
      
      console.log("Delete response:", response);
      
      // Remove from local state
      const updatedEvents = events.filter(event => event.id !== deletedId);
      setEvents(updatedEvents);

      setSnackbarMessage("Event deleted successfully");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      
    } catch (error) {
      console.error("Delete error:", error);
      console.error("GraphQL errors:", error.graphQLErrors);
      console.error("Network error:", error.networkError);

      let errorMessage = "Failed to delete event";
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        errorMessage = error.graphQLErrors[0].message;
      } else if (error.networkError) {
        errorMessage = "Network error occurred";
      }

      setSnackbarMessage(errorMessage);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  // Load events data with proper type handling
  useEffect(() => {
    if (!queryLoading && !queryError && queryData && queryData.events) {
      try {
        const eventsData = queryData.events.map((item) => ({
          id: String(item.id), // Ensure ID is string
          title: String(item.title || ""),
          details: String(item.details || ""),
          category: String(item.category || ""),
          date: item.date, // Keep as Date object from GraphQL
          status: Boolean(item.status).toString(), // Convert boolean to string for UI
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }));

        // Sort by date (most recent first)
        const sortedEvents = eventsData.length > 1 
          ? [...eventsData].sort((a, b) => new Date(b.date) - new Date(a.date))
          : eventsData;

        setEvents(sortedEvents);
      } catch (error) {
        console.error("Error processing events data:", error);
        setSnackbarMessage("Error loading events data");
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    }
  }, [queryLoading, queryError, queryData]);

  // Handle query errors
  useEffect(() => {
    if (queryError) {
      console.error("Query error:", queryError);
      setSnackbarMessage("Failed to load events");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  }, [queryError]);

  return (
    <>
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Typography variant="h5" sx={{ marginY: "10px" }}>
              Events Management
            </Typography>
            <BasicModal
              title="Add New Event"
              comp={
                <AddEvent
                  data={data}
                  setData={setData}
                  opt="add"
                  events={events}
                  setEvents={setEvents}
                  modalHandler={modalHandler}
                  setOpenUpdate={setOpenUpdate}
                  setOpen={setOpen}
                  setSnackbarMessage={setSnackbarMessage}
                  setSnackbarSeverity={setSnackbarSeverity}
                  setSnackbarOpen={setSnackbarOpen}
                />
              }
              btn={true}
              open={open}
              setOpen={setOpen}
            />
          </Box>

          <UpdateModal
            title="Update Event"
            comp={
              <AddEvent
                data={data}
                setData={setData}
                opt="update"
                events={events}
                setEvents={setEvents}
                modalHandler={modalHandler}
                setOpenUpdate={setOpenUpdate}
                setOpen={setOpen}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
              />
            }
            btn={false}
            openUpdate={openUpdate}
            setOpenUpdate={setOpenUpdate}
          />

          {queryLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center", marginTop: "50px" }}>
              <CircularProgress color="secondary" size={50} />
            </Box>
          ) : queryError ? (
            <Box sx={{ display: "flex", justifyContent: "center", marginTop: "50px" }}>
              <Typography variant="h6" color="error">
                Error loading events. Please try refreshing the page.
              </Typography>
            </Box>
          ) : (
            <>
              {events.length > 0 ? (
                <Box sx={{ maxWidth: "100%", overflow: "auto", marginTop: "20px" }}>
                  <DataTable
                    columns={columns}
                    rows={events}
                    setOpen={setOpenUpdate}
                    setIndex={setIndex}
                    setData={setData}
                    updateKey="title"
                    action={true}
                    deleteHandler={deleteHandler}
                    loading={deleteLoading}
                  />
                </Box>
              ) : (
                <Box sx={{ 
                  display: "flex", 
                  justifyContent: "center", 
                  alignItems: "center",
                  marginTop: "100px",
                  flexDirection: "column" 
                }}>
                  <Typography variant="h6" sx={{ marginBottom: "10px", color: "text.secondary" }}>
                    No events found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Create your first event to get started
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>

        <SnackbarComponent
          open={snackbarOpen}
          handleClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          message={snackbarMessage}
        />
      </Dashboard>
    </>
  );
};

export default withAdminAuth(Event);