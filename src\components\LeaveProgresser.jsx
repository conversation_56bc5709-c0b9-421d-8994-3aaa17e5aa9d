import React from "react";
import { Radial<PERSON><PERSON><PERSON><PERSON>, Radial<PERSON><PERSON> } from "recharts";
import dynamic from "next/dynamic";
import { useSession } from "next-auth/react";
import { useQuery } from "@apollo/client";
import { getUserLeave } from "@/Data/Leave";
import { useTheme } from "@emotion/react";

const LeaveProgresserComponent = ({ queryData }) => {
  const theme = useTheme();
  console.log("leaves", queryData)
  const data = [
    {
      name: "A",
      x: queryData ? queryData.getUserLeaves.medical : 0,
      fill: "#62B6F3",
    },
    {
      name: "B",
      x: queryData ? queryData.getUserLeaves.casual : 0,
      fill: "#A765E1",
    },

    {
      name: "C",
      x: 12,
      fill: theme.palette.background.tracker,
      stroke: theme.palette.background.tracker,
    },
  ];

  return (
    <RadialBarChart
      width={300}
      height={300}
      cx={150}
      cy={150}
      innerRadius="60%"
      outerRadius="80%"
      startAngle={270}
      endAngle={-90}
      data={data}
    >
      <RadialBar minAngle={30} dataKey="x" clockWise background />
    </RadialBarChart>
  );
};

const LeaveProgresser = dynamic(
  () => Promise.resolve(LeaveProgresserComponent),
  {
    ssr: false,
  }
);

export default LeaveProgresser;
